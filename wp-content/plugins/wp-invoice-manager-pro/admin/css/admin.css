/*
 * WordPress Invoice Manager Pro - Admin Styles
 */

:root {
    --wim-primary-color: #f47a45;
    --wim-secondary-color: #5f5f5f;
    --wim-background-color: #ffffff;
    --wim-light-border-color: #e7e7e7;
    --wim-text-color: #000000;
    --wim-background-alt-color: #f9fafb;

    --wim-success-color: #4CAF50;
    --wim-error-color: #F44336;
    --wim-warning-color: #FFC107;

    --wim-font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    --wim-border-radius: 4px;
    --wim-box-shadow: 0 1px 1px rgba(0, 0, 0, .04);
    --wim-spacing: 20px;
    --wim-spacing-half: 10px;
    --wim-spacing-quarter: 5px;
}

/* General Body Styles */
.wim-pro-admin-wrapper {
    font-family: var(--wim-font-family);
    font-size: 14px;
    line-height: 1.5;
    color: var(--wim-text-color);
}

.wim-pro-admin-wrapper h1,
.wim-pro-admin-wrapper h2,
.wim-pro-admin-wrapper h3,
.wim-pro-admin-wrapper h4,
.wim-pro-admin-wrapper h5,
.wim-pro-admin-wrapper h6 {
    color: var(--wim-secondary-color);
    font-weight: 600;
}

.wim-pro-admin-wrapper a {
    color: var(--wim-primary-color);
    text-decoration: none;
}

.wim-pro-admin-wrapper a:hover {
    text-decoration: underline;
}

/* Buttons */
.wim-pro-admin-wrapper .button {
    font-family: var(--wim-font-family);
    font-size: 13px;
    font-weight: 600;
    line-height: 1.5;
    padding: 8px 16px;
    border-radius: var(--wim-border-radius);
    border-width: 1px;
    border-style: solid;
    cursor: pointer;
    transition: background-color 0.3s, border-color 0.3s, color 0.3s;
}

.wim-pro-admin-wrapper .button-primary {
    background-color: var(--wim-primary-color);
    border-color: var(--wim-primary-color);
    color: #fff;
}

.wim-pro-admin-wrapper .button-primary:hover,
.wim-pro-admin-wrapper .button-primary:focus {
    background-color: #e06831;
    border-color: #e06831;
    color: #fff;
}

.wim-pro-admin-wrapper .button-secondary {
    background-color: var(--wim-secondary-color);
    border-color: var(--wim-secondary-color);
    color: #fff;
}

.wim-pro-admin-wrapper .button-secondary:hover,
.wim-pro-admin-wrapper .button-secondary:focus {
    background-color: #4a4a4a;
    border-color: #4a4a4a;
    color: #fff;
}

.wim-pro-admin-wrapper .button-secondary-outline {
    background-color: transparent;
    border-color: var(--wim-secondary-color);
    color: var(--wim-secondary-color);
}

.wim-pro-admin-wrapper .button-secondary-outline:hover,
.wim-pro-admin-wrapper .button-secondary-outline:focus {
    background-color: var(--wim-secondary-color);
    color: #fff;
}

/* Forms */
.wim-pro-admin-wrapper .form-table {
    background: var(--wim-background-color);
    border: 1px solid var(--wim-light-border-color);
    border-radius: var(--wim-border-radius);
    padding: var(--wim-spacing);
    box-shadow: var(--wim-box-shadow);
}

.wim-pro-admin-wrapper .form-table th {
    padding: 15px var(--wim-spacing) 15px 0;
    width: 200px;
    font-weight: 600;
}

.wim-pro-admin-wrapper .form-table td {
    padding: 15px 0;
}

.wim-pro-admin-wrapper input[type="text"],
.wim-pro-admin-wrapper input[type="email"],
.wim-pro-admin-wrapper input[type="number"],
.wim-pro-admin-wrapper input[type="password"],
.wim-pro-admin-wrapper input[type="search"],
.wim-pro-admin-wrapper textarea,
.wim-pro-admin-wrapper select {
    width: 100%;
    max-width: 400px;
    padding: 8px 12px;
    border: 1px solid var(--wim-light-border-color);
    border-radius: var(--wim-border-radius);
    box-shadow: inset 0 1px 2px rgba(0,0,0,.07);
}

.wim-pro-admin-wrapper input[type="text"]:focus,
.wim-pro-admin-wrapper input[type="email"]:focus,
.wim-pro-admin-wrapper input[type="number"]:focus,
.wim-pro-admin-wrapper input[type="password"]:focus,
.wim-pro-admin-wrapper input[type="search"]:focus,
.wim-pro-admin-wrapper textarea:focus,
.wim-pro-admin-wrapper select:focus {
    border-color: var(--wim-primary-color);
    box-shadow: 0 0 0 1px var(--wim-primary-color);
    outline: none;
}

/* Tables */
.wim-pro-admin-wrapper .wp-list-table {
    border: 1px solid var(--wim-light-border-color);
    border-radius: var(--wim-border-radius);
    box-shadow: var(--wim-box-shadow);
}

.wim-pro-admin-wrapper .wp-list-table thead th {
    background-color: var(--wim-background-alt-color);
    font-weight: 600;
}

.wim-pro-admin-wrapper .wp-list-table tbody tr:nth-child(odd) {
    background-color: var(--wim-background-alt-color);
}

.wim-pro-admin-wrapper .wp-list-table tbody tr:hover {
    background-color: #f0f0f0;
}

/* Tabs */
.wim-pro-admin-wrapper .nav-tab-wrapper {
    border-bottom: 1px solid var(--wim-light-border-color);
    padding-bottom: 0;
}

.wim-pro-admin-wrapper .nav-tab {
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    color: var(--wim-secondary-color);
    font-size: 14px;
    font-weight: 600;
    padding: 10px 0;
    margin: 0 20px 0 0;
}

.wim-pro-admin-wrapper .nav-tab:hover {
    border-bottom-color: var(--wim-secondary-color);
}

.wim-pro-admin-wrapper .nav-tab-active,
.wim-pro-admin-wrapper .nav-tab-active:hover {
    border-bottom-color: var(--wim-primary-color);
    color: var(--wim-primary-color);
}

/* Modals & Popups */
.wim-pro-modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.4);
}

.wim-pro-modal-content {
    background-color: var(--wim-background-color);
    margin: 10% auto;
    padding: var(--wim-spacing);
    border: 1px solid var(--wim-light-border-color);
    border-radius: var(--wim-border-radius);
    width: 80%;
    max-width: 600px;
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
}

.wim-pro-modal-header {
    padding-bottom: var(--wim-spacing-half);
    border-bottom: 1px solid var(--wim-light-border-color);
    margin-bottom: var(--wim-spacing);
}

.wim-pro-modal-header h2 {
    margin: 0;
}

.wim-pro-modal-close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.wim-pro-modal-close:hover,
.wim-pro-modal-close:focus {
    color: var(--wim-text-color);
}

/* Notifications */
.wim-pro-notice {
    padding: var(--wim-spacing-half) var(--wim-spacing);
    margin: var(--wim-spacing) 0;
    border-left-width: 4px;
    border-left-style: solid;
    border-radius: var(--wim-border-radius);
    box-shadow: var(--wim-box-shadow);
}

.wim-pro-notice.notice-success {
    background: #f0fff1;
    border-left-color: var(--wim-success-color);
}

.wim-pro-notice.notice-error {
    background: #fff0f0;
    border-left-color: var(--wim-error-color);
}

.wim-pro-notice.notice-warning {
    background: #fff8e5;
    border-left-color: var(--wim-warning-color);
}

/* Page specific styles */
.wim-pro-admin-wrapper .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--wim-spacing);
    padding-bottom: var(--wim-spacing);
    border-bottom: 1px solid var(--wim-light-border-color);
}

.wim-pro-admin-wrapper .page-header h1 {
    margin: 0;
}

/* Invoice Create/Edit Page */
#invoice-editor {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--wim-spacing);
}

#invoice-main-content {
    /* main content styles */
}

#invoice-sidebar {
    /* sidebar styles */
}

.invoice-meta-box {
    background: var(--wim-background-color);
    border: 1px solid var(--wim-light-border-color);
    border-radius: var(--wim-border-radius);
    margin-bottom: var(--wim-spacing);
}

.invoice-meta-box .hndle {
    font-size: 14px;
    padding: 8px 12px;
    margin: 0;
    line-height: 1.4;
    border-bottom: 1px solid var(--wim-light-border-color);
    font-weight: 600;
}

.invoice-meta-box .inside {
    padding: var(--wim-spacing);
}

/* ========================================
   STANDARDIZED BUTTON STYLES
   ======================================== */

.si-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.si-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    text-decoration: none;
}

.si-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.si-btn-primary {
    background: linear-gradient(135deg, #f47a45 0%, #e85d2b 100%);
    color: #ffffff;
}

.si-btn-primary:hover {
    background: linear-gradient(135deg, #e85d2b 0%, #d44a1f 100%);
    color: #ffffff;
}

.si-btn-secondary {
    background: #ffffff;
    color: #5f5f5f;
    border: 2px solid #e0e0e0;
}

.si-btn-secondary:hover {
    background: #f8f9fa;
    color: #5f5f5f;
    border-color: #d0d0d0;
}

.si-btn .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}
