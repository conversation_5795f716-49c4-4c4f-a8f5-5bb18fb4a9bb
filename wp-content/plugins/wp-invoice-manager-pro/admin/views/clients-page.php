<?php
/**
 * Clients Page Template
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get clients and search parameters
$client_manager = new SI_Client();
$search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';
$paged = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$per_page = 20;
$offset = ($paged - 1) * $per_page;

// Get clients with search
$clients = $client_manager->si_get_clients(array(
    'search' => $search,
    'limit' => $per_page,
    'offset' => $offset
));

// Get total count for pagination
$total_clients = count($client_manager->si_get_clients(array('search' => $search)));
$total_pages = ceil($total_clients / $per_page);

// Get statistics
$all_clients = $client_manager->si_get_clients();
$total_count = count($all_clients);

// Get invoice manager for client statistics
$invoice_manager = new SI_Invoice();
$client_stats = array();
$active_clients = 0;
$total_revenue = 0;

foreach ($all_clients as $client) {
    $client_invoices = $invoice_manager->si_get_invoices(array('client_id' => $client->id));
    $client_revenue = array_sum(array_map(function($inv) { return floatval($inv->total_amount); }, $client_invoices));

    $client_stats[$client->id] = array(
        'total_invoices' => count($client_invoices),
        'total_amount' => $client_revenue,
        'last_invoice' => !empty($client_invoices) ? max(array_map(function($inv) { return strtotime($inv->created_at); }, $client_invoices)) : null
    );

    if (count($client_invoices) > 0) {
        $active_clients++;
    }
    $total_revenue += $client_revenue;
}

// Set up page header variables
$page_title = __('Clients', 'wp-invoice-manager-pro');
$page_subtitle = __('Manage your client relationships and contact information', 'wp-invoice-manager-pro');
$page_icon = 'dashicons-groups';
$header_actions = array(
    array(
        'type' => 'button',
        'text' => __('Add New Client', 'wp-invoice-manager-pro'),
        'icon' => 'dashicons-plus-alt',
        'class' => 'si-btn si-btn-primary si-add-client-btn'
    )
);

// Include common header
include WIMP_PLUGIN_PATH . 'admin/views/common/page-header.php';
?>

    <!-- Quick Stats -->
    <div class="si-quick-stats">
        <div class="si-stat-item">
            <div class="si-stat-icon si-stat-blue">
                <span class="dashicons dashicons-groups"></span>
            </div>
            <div class="si-stat-info">
                <span class="si-stat-number"><?php echo esc_html($total_count); ?></span>
                <span class="si-stat-label"><?php echo esc_html__('Total Clients', 'simple-invoice'); ?></span>
            </div>
        </div>

        <div class="si-stat-item">
            <div class="si-stat-icon si-stat-green">
                <span class="dashicons dashicons-businessman"></span>
            </div>
            <div class="si-stat-info">
                <span class="si-stat-number"><?php echo esc_html($active_clients); ?></span>
                <span class="si-stat-label"><?php echo esc_html__('Active Clients', 'simple-invoice'); ?></span>
            </div>
        </div>

        <div class="si-stat-item">
            <div class="si-stat-icon si-stat-orange">
                <span class="dashicons dashicons-chart-line"></span>
            </div>
            <div class="si-stat-info">
                <span class="si-stat-number"><?php echo esc_html(si_format_currency($total_revenue)); ?></span>
                <span class="si-stat-label"><?php echo esc_html__('Total Revenue', 'simple-invoice'); ?></span>
            </div>
        </div>

        <div class="si-stat-item">
            <div class="si-stat-icon si-stat-purple">
                <span class="dashicons dashicons-media-text"></span>
            </div>
            <div class="si-stat-info">
                <span class="si-stat-number"><?php echo esc_html(array_sum(array_column($client_stats, 'total_invoices'))); ?></span>
                <span class="si-stat-label"><?php echo esc_html__('Total Invoices', 'simple-invoice'); ?></span>
            </div>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="si-clients-content">
        <div class="si-search-section">
            <form method="get" action="" class="si-search-form">
                <input type="hidden" name="page" value="wimp-clients" />

                <div class="si-search-container">
                    <div class="si-search-input-wrapper">
                        <span class="dashicons dashicons-search si-search-icon"></span>
                        <input type="search"
                               name="search"
                               id="si-client-search"
                               value="<?php echo esc_attr($search); ?>"
                               placeholder="<?php echo esc_attr__('Search clients by name, business, or email...', 'simple-invoice'); ?>"
                               class="si-search-input" />
                        <?php if ($search): ?>
                            <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-clients')); ?>" class="si-search-clear">
                                <span class="dashicons dashicons-dismiss"></span>
                            </a>
                        <?php endif; ?>
                    </div>

                    <button type="submit" class="si-btn si-btn-primary si-search-btn">
                        <span class="dashicons dashicons-search"></span>
                        <?php echo esc_html__('Search', 'simple-invoice'); ?>
                    </button>
                </div>

                <?php if ($search): ?>
                    <div class="si-search-results-info">
                        <span class="si-results-text">
                            <?php echo esc_html(sprintf(__('Found %d clients matching "%s"', 'simple-invoice'), $total_clients, $search)); ?>
                        </span>
                    </div>
                <?php endif; ?>
            </form>
        </div>

        <!-- Clients List -->
        <div class="si-clients-list-wrapper">
            <?php if (!empty($clients)): ?>
                <div class="si-clients-list">
                    <div class="si-list-header">
                        <h3><?php echo esc_html__('Client List', 'simple-invoice'); ?></h3>
                        <div class="si-list-info">
                            <?php if ($search): ?>
                                <?php echo esc_html(sprintf(__('Showing %d of %d clients matching "%s"', 'simple-invoice'), count($clients), $total_clients, $search)); ?>
                            <?php else: ?>
                                <?php echo esc_html(sprintf(__('Showing %d clients', 'simple-invoice'), count($clients))); ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="si-clients-list-container">
                        <?php foreach ($clients as $client): ?>
                            <?php
                            $stats = isset($client_stats[$client->id]) ? $client_stats[$client->id] : array('total_invoices' => 0, 'total_amount' => 0, 'last_invoice' => null);
                            ?>
                            <div class="si-client-item" data-client-id="<?php echo esc_attr($client->id); ?>">
                                <div class="si-client-row">
                                    <!-- Client Info -->
                                    <div class="si-client-main">
                                        <div class="si-client-avatar">
                                            <span class="dashicons dashicons-businessman"></span>
                                        </div>
                                        <div class="si-client-details">
                                            <h4 class="si-client-name"><?php echo esc_html($client->name); ?></h4>
                                            <div class="si-client-meta">
                                                <?php if (!empty($client->business_name)): ?>
                                                    <span class="si-business-name"><?php echo esc_html($client->business_name); ?></span>
                                                <?php else: ?>
                                                    <span class="si-individual"><?php echo esc_html__('Individual Client', 'simple-invoice'); ?></span>
                                                <?php endif; ?>
                                                <span class="si-separator">•</span>
                                                <span class="si-date"><?php echo esc_html(date('M j, Y', strtotime($client->created_at))); ?></span>
                                                <span class="si-separator">•</span>
                                                <?php if ($stats['total_invoices'] > 0): ?>
                                                    <span class="si-status si-active"><?php echo esc_html__('Active', 'simple-invoice'); ?></span>
                                                <?php else: ?>
                                                    <span class="si-status si-new"><?php echo esc_html__('New', 'simple-invoice'); ?></span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Contact Info -->
                                    <div class="si-client-contact">
                                        <?php if (!empty($client->email)): ?>
                                            <div class="si-contact-item">
                                                <span class="dashicons dashicons-email"></span>
                                                <a href="mailto:<?php echo esc_attr($client->email); ?>"><?php echo esc_html($client->email); ?></a>
                                            </div>
                                        <?php endif; ?>
                                        <?php if (!empty($client->contact_number)): ?>
                                            <div class="si-contact-item">
                                                <span class="dashicons dashicons-phone"></span>
                                                <a href="tel:<?php echo esc_attr($client->contact_number); ?>"><?php echo esc_html($client->contact_number); ?></a>
                                            </div>
                                        <?php endif; ?>
                                        <?php if (!empty($client->gstin)): ?>
                                            <div class="si-contact-item">
                                                <span class="dashicons dashicons-id"></span>
                                                <span><?php echo esc_html($client->gstin); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Stats -->
                                    <div class="si-client-stats">
                                        <div class="si-stat">
                                            <span class="si-stat-number"><?php echo esc_html($stats['total_invoices']); ?></span>
                                            <span class="si-stat-label"><?php echo esc_html__('Invoices', 'simple-invoice'); ?></span>
                                        </div>
                                        <div class="si-stat">
                                            <span class="si-stat-number"><?php echo esc_html(si_format_currency($stats['total_amount'])); ?></span>
                                            <span class="si-stat-label"><?php echo esc_html__('Revenue', 'simple-invoice'); ?></span>
                                        </div>
                                    </div>

                                    <!-- Actions -->
                                    <div class="si-client-actions">
                                        <button type="button" class="button button-small si-edit-client" data-client-id="<?php echo esc_attr($client->id); ?>" title="<?php echo esc_attr__('Edit Client', 'simple-invoice'); ?>">
                                            <span class="dashicons dashicons-edit"></span>
                                            <?php echo esc_html__('Edit', 'simple-invoice'); ?>
                                        </button>
                                        <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-create-invoice&client_id=' . $client->id)); ?>" class="button button-small button-primary" title="<?php echo esc_attr__('Create Invoice', 'wp-invoice-manager-pro'); ?>">
                                            <span class="dashicons dashicons-plus-alt"></span>
                                            <?php echo esc_html__('Invoice', 'simple-invoice'); ?>
                                        </a>
                                        <button type="button" class="button button-small button-link-delete si-delete-client" data-client-id="<?php echo esc_attr($client->id); ?>" title="<?php echo esc_attr__('Delete Client', 'simple-invoice'); ?>">
                                            <span class="dashicons dashicons-trash"></span>
                                            <?php echo esc_html__('Delete', 'simple-invoice'); ?>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php else: ?>
                <!-- Empty State -->
                <div class="si-empty-state">
                    <div class="si-empty-content">
                        <?php if ($search): ?>
                            <div class="si-empty-icon">
                                <span class="dashicons dashicons-search"></span>
                            </div>
                            <h3 class="si-empty-title"><?php echo esc_html__('No clients found', 'simple-invoice'); ?></h3>
                            <p class="si-empty-description"><?php echo sprintf(esc_html__('No clients match your search for "%s". Try adjusting your search terms or add a new client.', 'simple-invoice'), '<strong>' . esc_html($search) . '</strong>'); ?></p>
                            <div class="si-empty-actions">
                                <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-clients')); ?>" class="si-btn si-btn-secondary">
                                    <span class="dashicons dashicons-dismiss"></span>
                                    <?php echo esc_html__('Clear Search', 'simple-invoice'); ?>
                                </a>
                                <a href="#" class="si-btn si-btn-primary si-add-client-btn">
                                    <span class="dashicons dashicons-plus-alt"></span>
                                    <?php echo esc_html__('Add New Client', 'simple-invoice'); ?>
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="si-empty-icon">
                                <span class="dashicons dashicons-groups"></span>
                            </div>
                            <h3 class="si-empty-title"><?php echo esc_html__('No clients yet', 'simple-invoice'); ?></h3>
                            <p class="si-empty-description"><?php echo esc_html__('Start building your client base by adding your first client. You can then create invoices and track your business relationships.', 'simple-invoice'); ?></p>
                            <div class="si-empty-actions">
                                <a href="#" class="si-btn si-btn-primary si-add-client-btn">
                                    <span class="dashicons dashicons-plus-alt"></span>
                                    <?php echo esc_html__('Add Your First Client', 'simple-invoice'); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <div class="si-pagination">
                <?php
                $pagination_args = array(
                    'base' => add_query_arg('paged', '%#%'),
                    'format' => '',
                    'prev_text' => __('&laquo; Previous', 'simple-invoice'),
                    'next_text' => __('Next &raquo;', 'simple-invoice'),
                    'current' => $paged,
                    'total' => $total_pages,
                    'add_args' => array(
                        'search' => $search
                    )
                );
                echo paginate_links($pagination_args);
                ?>
            </div>
        <?php endif; ?>
    </div>

<?php
// Include common footer
include WIMP_PLUGIN_PATH . 'admin/views/common/page-footer.php';
?>

<!-- Modern Add/Edit Client Modal -->
<div id="si-client-modal" class="si-modal si-client-modal" style="display: none;">
    <div class="si-modal-content">
        <!-- Modal Header -->
        <div class="si-modal-header">
            <div class="si-modal-header-content">
                <div class="si-modal-icon">
                    <span class="dashicons dashicons-businessman"></span>
                </div>
                <div class="si-modal-title-section">
                    <h2 id="si-client-modal-title"><?php echo esc_html__('Add New Client', 'simple-invoice'); ?></h2>
                    <p class="si-modal-subtitle"><?php echo esc_html__('Enter client information to create a new client profile', 'simple-invoice'); ?></p>
                </div>
            </div>
            <button type="button" class="si-modal-close">
                <span class="dashicons dashicons-no-alt"></span>
            </button>
        </div>

        <!-- Modal Body -->
        <div class="si-modal-body">
            <form id="si-client-form">
                <input type="hidden" id="si-client-id" name="client_id" value="" />

                <!-- Personal Information Section -->
                <div class="si-form-section">
                    <div class="si-section-header">
                        <h3>
                            <span class="dashicons dashicons-admin-users"></span>
                            <?php echo esc_html__('Personal Information', 'simple-invoice'); ?>
                        </h3>
                        <p class="si-section-description"><?php echo esc_html__('Basic client details and contact information', 'simple-invoice'); ?></p>
                    </div>

                    <div class="si-form-grid">
                        <div class="si-form-group si-form-group-full">
                            <label for="si-client-name" class="si-form-label">
                                <span class="dashicons dashicons-admin-users"></span>
                                <?php echo esc_html__('Full Name', 'simple-invoice'); ?>
                                <span class="si-required">*</span>
                            </label>
                            <input type="text"
                                   id="si-client-name"
                                   name="name"
                                   class="si-form-input"
                                   required
                                   placeholder="<?php echo esc_attr__('Enter client full name', 'simple-invoice'); ?>" />
                            <div class="si-form-help"><?php echo esc_html__('The primary contact person name', 'simple-invoice'); ?></div>
                        </div>

                        <div class="si-form-group">
                            <label for="si-client-email" class="si-form-label">
                                <span class="dashicons dashicons-email-alt"></span>
                                <?php echo esc_html__('Email Address', 'simple-invoice'); ?>
                            </label>
                            <input type="email"
                                   id="si-client-email"
                                   name="email"
                                   class="si-form-input"
                                   placeholder="<?php echo esc_attr__('<EMAIL>', 'simple-invoice'); ?>" />
                            <div class="si-form-help"><?php echo esc_html__('Primary email for invoices and communication', 'simple-invoice'); ?></div>
                        </div>

                        <div class="si-form-group">
                            <label for="si-client-phone" class="si-form-label">
                                <span class="dashicons dashicons-phone"></span>
                                <?php echo esc_html__('Phone Number', 'simple-invoice'); ?>
                            </label>
                            <input type="tel"
                                   id="si-client-phone"
                                   name="contact_number"
                                   class="si-form-input"
                                   placeholder="<?php echo esc_attr__('Phone number with country code', 'simple-invoice'); ?>" />
                            <div class="si-form-help"><?php echo esc_html__('Contact number for quick communication', 'simple-invoice'); ?></div>
                        </div>
                    </div>
                </div>

                <!-- Business Information Section -->
                <div class="si-form-section">
                    <div class="si-section-header">
                        <h3>
                            <span class="dashicons dashicons-building"></span>
                            <?php echo esc_html__('Business Information', 'simple-invoice'); ?>
                        </h3>
                        <p class="si-section-description"><?php echo esc_html__('Company details and tax information (optional)', 'simple-invoice'); ?></p>
                    </div>

                    <div class="si-form-grid">
                        <div class="si-form-group">
                            <label for="si-client-business" class="si-form-label">
                                <span class="dashicons dashicons-building"></span>
                                <?php echo esc_html__('Business Name', 'simple-invoice'); ?>
                            </label>
                            <input type="text"
                                   id="si-client-business"
                                   name="business_name"
                                   class="si-form-input"
                                   placeholder="<?php echo esc_attr__('Company or business name', 'simple-invoice'); ?>" />
                            <div class="si-form-help"><?php echo esc_html__('Official business or company name', 'simple-invoice'); ?></div>
                        </div>

                        <div class="si-form-group">
                            <label for="si-client-gstin" class="si-form-label">
                                <span class="dashicons dashicons-id-alt"></span>
                                <?php echo esc_html__('GSTIN / Tax ID', 'simple-invoice'); ?>
                            </label>
                            <input type="text"
                                   id="si-client-gstin"
                                   name="gstin"
                                   class="si-form-input"
                                   placeholder="<?php echo esc_attr__('Tax identification number', 'simple-invoice'); ?>" />
                            <div class="si-form-help"><?php echo esc_html__('GST number or tax identification', 'simple-invoice'); ?></div>
                        </div>

                        <div class="si-form-group si-form-group-full">
                            <label for="si-client-address" class="si-form-label">
                                <span class="dashicons dashicons-location-alt"></span>
                                <?php echo esc_html__('Business Address', 'simple-invoice'); ?>
                            </label>
                            <textarea id="si-client-address"
                                      name="address"
                                      rows="3"
                                      class="si-form-textarea"
                                      placeholder="<?php echo esc_attr__('Complete business address with city, state, and postal code', 'simple-invoice'); ?>"></textarea>
                            <div class="si-form-help"><?php echo esc_html__('Full address for invoicing and correspondence', 'simple-invoice'); ?></div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Modal Footer -->
        <div class="si-modal-footer">
            <div class="si-modal-footer-content">
                <button type="button" class="si-btn si-btn-primary" id="si-save-client">
                    <span class="dashicons dashicons-yes-alt"></span>
                    <span class="si-btn-text"><?php echo esc_html__('Save Client', 'simple-invoice'); ?></span>
                </button>
            </div>
        </div>
    </div>
</div>
