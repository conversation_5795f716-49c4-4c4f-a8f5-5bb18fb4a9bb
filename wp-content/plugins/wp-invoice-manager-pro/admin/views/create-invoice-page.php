<?php
/**
 * Create Invoice Page Template
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get clients and templates
$client_manager = new SI_Client();
$template_manager = new SI_Template();
$clients = $client_manager->si_get_clients();
$templates = $template_manager->si_get_templates();

// Get selected template if provided
$selected_template_id = isset($_GET['template_id']) ? intval($_GET['template_id']) : 0;
$selected_template = null;

if ($selected_template_id > 0) {
    $selected_template = $template_manager->si_get_template($selected_template_id);
}

// Set up page header variables
$page_title = __('Create New Invoice', 'wp-invoice-manager-pro');
$page_subtitle = __('Generate professional invoices for your clients', 'wp-invoice-manager-pro');
$page_icon = 'dashicons-plus-alt';
$header_actions = array(
    array(
        'type' => 'link',
        'url' => admin_url('admin.php?page=wimp-invoices'),
        'text' => __('View All Invoices', 'wp-invoice-manager-pro'),
        'icon' => 'dashicons-list-view',
        'class' => 'si-btn si-btn-secondary'
    )
);

// Include common header
include WIMP_PLUGIN_PATH . 'admin/views/common/page-header.php';
?>

    <?php if (empty($templates)): ?>
        <div class="notice notice-warning">
            <p><strong><?php echo esc_html__('No Invoice Templates Found!', 'wp-invoice-manager-pro'); ?></strong></p>
            <p><?php echo esc_html__('You need to create at least one invoice template before creating invoices.', 'wp-invoice-manager-pro'); ?></p>
            <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-templates')); ?>" class="si-btn si-btn-primary">
                <span class="dashicons dashicons-plus-alt"></span>
                <?php echo esc_html__('Create Your First Template', 'wp-invoice-manager-pro'); ?>
            </a>
        </div>
    <?php elseif (empty($clients)): ?>
        <div class="si-empty-state si-warning-state">
            <div class="si-empty-icon">
                <span class="dashicons dashicons-warning"></span>
            </div>
            <h3><?php echo esc_html__('No Clients Found!', 'wp-invoice-manager-pro'); ?></h3>
            <p><?php echo esc_html__('You need to add at least one client before creating invoices.', 'wp-invoice-manager-pro'); ?></p>
            <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-clients')); ?>" class="si-btn si-btn-primary">
                <span class="dashicons dashicons-plus-alt"></span>
                <?php echo esc_html__('Add Your First Client', 'wp-invoice-manager-pro'); ?>
            </a>
        </div>
    <?php else: ?>

        <form id="si-invoice-form">
            <div id="poststuff">
                <div id="post-body" class="metabox-holder columns-2">
                    <div id="post-body-content">
                        <div class="meta-box-sortables ui-sortable">
                            <div class="postbox">
                                <h2 class="hndle">
                                    <span><?php echo esc_html__('Invoice Configuration', 'wp-invoice-manager-pro'); ?></span>
                                </h2>
                                <div class="inside">
                                    <table class="form-table">
                                        <tr>
                                            <th scope="row">
                                                <label for="si-template-select">
                                                    <?php echo esc_html__('Invoice Template', 'wp-invoice-manager-pro'); ?>
                                                    <span class="description">(<?php echo esc_html__('required', 'wp-invoice-manager-pro'); ?>)</span>
                                                </label>
                                            </th>
                                            <td>
                                                <select id="si-template-select" name="template_id" class="si-select" required>
                                                    <option value=""><?php echo esc_html__('Choose an invoice template...', 'wp-invoice-manager-pro'); ?></option>
                                                    <?php foreach ($templates as $template): ?>
                                                        <option value="<?php echo esc_attr($template->id); ?>"
                                                                <?php selected($template->id, $selected_template_id); ?>
                                                                data-design="<?php echo esc_attr($template->design); ?>">
                                                            <?php echo esc_html($template->name); ?>
                                                            <?php if (!empty($template->design) && $template->design !== 'classic'): ?>
                                                                (<?php echo esc_html(ucwords(str_replace('-', ' ', $template->design))); ?> Design)
                                                            <?php endif; ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                                <span class="si-field-help"><?php echo esc_html__('Choose the template and field configuration for your invoice', 'wp-invoice-manager-pro'); ?></span>
                                            </td>
                                        </tr>

                                        <tr>
                                            <th scope="row">
                                                <label for="si-client-select">
                                                    <?php echo esc_html__('Client', 'wp-invoice-manager-pro'); ?>
                                                    <span class="description">(<?php echo esc_html__('required', 'wp-invoice-manager-pro'); ?>)</span>
                                                </label>
                                            </th>
                                            <td>
                                                <div class="si-client-select-wrapper">
                                                    <select id="si-client-select" name="client_id" class="regular-text" required>
                                                        <option value=""><?php echo esc_html__('Choose a client...', 'wp-invoice-manager-pro'); ?></option>
                                                        <?php foreach ($clients as $client): ?>
                                                            <option value="<?php echo esc_attr($client->id); ?>">
                                                                <?php echo esc_html($client->name); ?>
                                                                <?php if (!empty($client->business_name)): ?>
                                                                    (<?php echo esc_html($client->business_name); ?>)
                                                                <?php endif; ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                    <button type="button" class="button si-add-client-btn" title="<?php echo esc_attr__('Add New Client', 'wp-invoice-manager-pro'); ?>">
                                                        <span class="dashicons dashicons-plus-alt"></span>
                                                    </button>
                                                </div>
                                                <p class="description"><?php echo esc_html__('Select the client who will receive this invoice', 'wp-invoice-manager-pro'); ?></p>
                                            </td>
                                        </tr>

                                        <tr>
                                            <th scope="row">
                                                <label for="si-invoice-number">
                                                    <?php echo esc_html__('Invoice Number', 'wp-invoice-manager-pro'); ?>
                                                </label>
                                            </th>
                                            <td>
                                                <input type="text"
                                                       id="si-invoice-number"
                                                       name="invoice_number"
                                                       class="si-input"
                                                       placeholder="<?php echo esc_attr__('Auto-generated if empty', 'wp-invoice-manager-pro'); ?>" />
                                                <p class="description"><?php echo esc_html__('Leave empty to auto-generate invoice number', 'wp-invoice-manager-pro'); ?></p>
                                            </td>
                                        </tr>

                                        <tr>
                                            <th scope="row">
                                                <label for="si-invoice-date">
                                                    <?php echo esc_html__('Invoice Date', 'wp-invoice-manager-pro'); ?>
                                                </label>
                                            </th>
                                            <td>
                                                <input type="date"
                                                       id="si-invoice-date"
                                                       name="invoice_date"
                                                       value="<?php echo esc_attr(date('Y-m-d')); ?>"
                                                       class="si-input" />
                                                <span class="si-field-help"><?php echo esc_html__('Date when the invoice is issued', 'wp-invoice-manager-pro'); ?></span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Invoice Items Section -->
                            <div class="postbox">
                                <h2 class="hndle">
                                    <span><?php echo esc_html__('Invoice Items', 'wp-invoice-manager-pro'); ?></span>
                                </h2>
                                <div class="inside">
                                    <div class="si-items-header">
                                        <p><?php echo esc_html__('Add products or services to your invoice', 'wp-invoice-manager-pro'); ?></p>
                                        <button type="button" class="button button-primary" id="si-add-item">
                                            <span class="dashicons dashicons-plus-alt"></span>
                                            <?php echo esc_html__('Add Item', 'wp-invoice-manager-pro'); ?>
                                        </button>
                                    </div>

                                    <div class="table-responsive">
                                        <table class="wp-list-table widefat fixed striped" id="si-invoice-items">
                                            <thead>
                                                <tr>
                                                    <th scope="col" width="5%"><?php echo esc_html__('#', 'wp-invoice-manager-pro'); ?></th>
                                                    <th scope="col"><?php echo esc_html__('Description', 'wp-invoice-manager-pro'); ?></th>
                                                    <th scope="col" width="10%"><?php echo esc_html__('Qty', 'wp-invoice-manager-pro'); ?></th>
                                                    <th scope="col" width="15%"><?php echo esc_html__('Rate', 'wp-invoice-manager-pro'); ?></th>
                                                    <th scope="col" width="15%"><?php echo esc_html__('Total', 'wp-invoice-manager-pro'); ?></th>
                                                    <th scope="col" width="8%"><?php echo esc_html__('Actions', 'wp-invoice-manager-pro'); ?></th>
                                                </tr>
                                            </thead>
                                            <tbody id="si-items-body">
                                                <!-- Items will be added here -->
                                            </tbody>
                                        </table>
                                    </div>

                                    <div class="notice notice-warning notice-alt inline" id="si-items-empty">
                                        <p><?php echo esc_html__('No items added yet. Click "Add Item" to start building your invoice', 'wp-invoice-manager-pro'); ?></p>
                                    </div>
                                </div>
                            </div>

                            <!-- Invoice Summary Section -->
                            <div class="postbox">
                                <h2 class="hndle">
                                    <span><?php echo esc_html__('Invoice Summary', 'wp-invoice-manager-pro'); ?></span>
                                </h2>
                                <div class="inside">
                                    <table class="form-table">
                                        <tr>
                                            <th scope="row"><?php echo esc_html__('Subtotal', 'wp-invoice-manager-pro'); ?></th>
                                            <td><span id="si-subtotal">$0.00</span></td>
                                        </tr>
                                        <tr>
                                            <th scope="row"><?php echo esc_html__('Tax Rate (%)', 'wp-invoice-manager-pro'); ?></th>
                                            <td>
                                                <input type="number" class="small-text" id="si-tax-rate" name="tax_rate" step="0.01" min="0" value="0" />
                                                <span id="si-tax-amount">$0.00</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th scope="row"><?php echo esc_html__('Discount', 'wp-invoice-manager-pro'); ?></th>
                                            <td>
                                                <input type="number" class="small-text" id="si-discount" name="discount" step="0.01" min="0" value="0" />
                                            </td>
                                        </tr>
                                        <tr>
                                            <th scope="row"><?php echo esc_html__('Shipping', 'wp-invoice-manager-pro'); ?></th>
                                            <td>
                                                <input type="number" class="small-text" id="si-shipping" name="shipping" step="0.01" min="0" value="0" />
                                            </td>
                                        </tr>
                                        <tr>
                                            <th scope="row"><strong><?php echo esc_html__('Total Amount', 'wp-invoice-manager-pro'); ?></strong></th>
                                            <td><strong><span id="si-total-amount">$0.00</span></strong></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Submit Section -->
                            <div class="submitbox">
                                <div id="major-publishing-actions">
                                    <div id="publishing-action">
                                        <button type="button" class="button button-primary button-large" id="si-create-invoice">
                                            <?php echo esc_html__('Create & Download PDF', 'wp-invoice-manager-pro'); ?>
                                        </button>
                                    </div>
                                    <div class="clear"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    <?php endif; ?>

<?php
// Include common footer
include WIMP_PLUGIN_PATH . 'admin/views/common/page-footer.php';
?>
