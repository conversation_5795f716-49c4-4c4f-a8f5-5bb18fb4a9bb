<?php
/**
 * Create Invoice Page Template
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get clients and templates
$client_manager = new SI_Client();
$template_manager = new SI_Template();
$clients = $client_manager->si_get_clients();
$templates = $template_manager->si_get_templates();

// Get selected template if provided
$selected_template_id = isset($_GET['template_id']) ? intval($_GET['template_id']) : 0;
$selected_template = null;

if ($selected_template_id > 0) {
    $selected_template = $template_manager->si_get_template($selected_template_id);
}
?>

<div class="wrap">
    <h1 class="wp-heading-inline"><?php echo esc_html__('Create New Invoice', 'wp-invoice-manager-pro'); ?></h1>
    <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-invoices')); ?>" class="page-title-action">
        <span class="dashicons dashicons-list-view"></span>
        <?php echo esc_html__('View All Invoices', 'wp-invoice-manager-pro'); ?>
    </a>
    <hr class="wp-header-end">

    <?php if (empty($templates)): ?>
        <div class="notice notice-warning">
            <p><strong><?php echo esc_html__('No Invoice Layouts Found!', 'simple-invoice'); ?></strong></p>
            <p><?php echo esc_html__('You need to create at least one invoice layout before creating invoices. Layouts combine visual designs with field configurations to define how your invoices will look and what information they contain.', 'simple-invoice'); ?></p>
            <div class="si-help-note">
                <span class="dashicons dashicons-info"></span>
                <strong><?php echo esc_html__('What\'s the difference?', 'simple-invoice'); ?></strong><br>
                <span><?php echo esc_html__('• Designs = Visual appearance (colors, fonts, layout style)', 'simple-invoice'); ?></span><br>
                <span><?php echo esc_html__('• Layouts = Designs + Field configuration (what info to show)', 'simple-invoice'); ?></span>
            </div>
            <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-templates')); ?>" class="si-btn si-btn-primary">
                <span class="dashicons dashicons-plus-alt"></span>
                <?php echo esc_html__('Create Your First Layout', 'simple-invoice'); ?>
            </a>
        </div>
    <?php elseif (empty($clients)): ?>
        <div class="si-empty-state si-warning-state">
            <div class="si-empty-icon">
                <span class="dashicons dashicons-warning"></span>
            </div>
            <h3><?php echo esc_html__('No Clients Found!', 'simple-invoice'); ?></h3>
            <p><?php echo esc_html__('You need to add at least one client before creating invoices. Clients are the recipients of your invoices.', 'simple-invoice'); ?></p>
            <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-clients')); ?>" class="si-btn si-btn-primary">
                <span class="dashicons dashicons-plus-alt"></span>
                <?php echo esc_html__('Add Your First Client', 'simple-invoice'); ?>
            </a>
        </div>
    <?php else: ?>

        <form id="si-invoice-form">
            
            <div id="poststuff">
                <div id="post-body" class="metabox-holder columns-2">
                    <div id="post-body-content">
                        <div class="meta-box-sortables ui-sortable">
                            <div class="postbox">
                                <h2 class="hndle">
                                    <span><?php echo esc_html__('Invoice Configuration', 'simple-invoice'); ?></span>
                                </h2>
                                <div class="inside">

                                <table class="form-table">
                                    <tr>
                                        <th scope="row">
                                            <label for="si-template-select">
                                                <?php echo esc_html__('Invoice Layout', 'simple-invoice'); ?>
                                                <span class="description">(<?php echo esc_html__('required', 'simple-invoice'); ?>)</span>
                                            </label>
                                        </th>
                                        <td>
                            <select id="si-template-select" name="template_id" class="si-select" required>
                                <option value=""><?php echo esc_html__('Choose an invoice layout...', 'simple-invoice'); ?></option>
                                <?php foreach ($templates as $template): ?>
                                    <option value="<?php echo esc_attr($template->id); ?>"
                                            <?php selected($template->id, $selected_template_id); ?>
                                            data-design="<?php echo esc_attr($template->design); ?>">
                                        <?php echo esc_html($template->name); ?>
                                        <?php if (!empty($template->design) && $template->design !== 'classic'): ?>
                                            (<?php echo esc_html(ucwords(str_replace('-', ' ', $template->design))); ?> Design)
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <span class="si-field-help"><?php echo esc_html__('Choose the layout and field configuration for your invoice', 'simple-invoice'); ?></span>
                            <div class="si-template-preview" id="si-template-preview" >
                                <div class="si-preview-header">
                                    <span class="dashicons dashicons-visibility"></span>
                                    <span class="si-preview-title"><?php echo esc_html__('Selected Layout Preview', 'simple-invoice'); ?></span>
                                </div>
                                <div class="si-preview-content">
                                    <div class="si-preview-design">
                                        <span class="si-design-badge" id="si-design-badge"></span>
                                    </div>
                                    <div class="si-preview-info">
                                        <h4 id="si-template-name"></h4>
                                        <p id="si-template-description"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="si-form-field">
                        <label for="si-client-select" class="si-field-label">
                            <span class="dashicons dashicons-businessperson"></span>
                            <?php echo esc_html__('Client', 'simple-invoice'); ?>
                            <span class="required">*</span>
                        </label>
                        <div class="si-field-wrapper">
                                    <div class="si-client-select-wrapper">
                                        <select id="si-client-select" name="client_id" class="regular-text" required>
                                    <option value=""><?php echo esc_html__('Choose a client...', 'simple-invoice'); ?></option>
                                    <?php foreach ($clients as $client): ?>
                                        <option value="<?php echo esc_attr($client->id); ?>">
                                            <?php echo esc_html($client->name); ?>
                                            <?php if (!empty($client->business_name)): ?>
                                                (<?php echo esc_html($client->business_name); ?>)
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <button type="button" class="button thickbox" title="<?php echo esc_attr__('Add New Client', 'simple-invoice'); ?>" 
                                    onclick="tb_show('<?php echo esc_js(__('Add New Client', 'simple-invoice')); ?>', '#TB_inline?width=600&height=550&inlineId=si-quick-client-modal');">
                                    <span class="dashicons dashicons-plus-alt"></span>
                                </button>
                            </div>
                            <p class="description"><?php echo esc_html__('Select the client who will receive this invoice', 'simple-invoice'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="si-invoice-number">
                                <?php echo esc_html__('Invoice Number', 'simple-invoice'); ?>
                            </label>
                        </th>
                        <td>
                            <input type="text"
                                   id="si-invoice-number"
                                   name="invoice_number"
                                   class="si-input"
                                   placeholder="<?php echo esc_attr__('Auto-generated if empty', 'simple-invoice'); ?>" />
                            <p class="description"><?php echo esc_html__('Leave empty to auto-generate invoice number', 'simple-invoice'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="si-invoice-date">
                                <?php echo esc_html__('Invoice Date', 'simple-invoice'); ?>
                            </label>
                        </th>
                        <td>
                            <input type="date"
                                   id="si-invoice-date"
                                   name="invoice_date"
                                   value="<?php echo esc_attr(date('Y-m-d')); ?>"
                                   class="si-input" />
                            <span class="si-field-help"><?php echo esc_html__('Date when the invoice is issued', 'simple-invoice'); ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="postbox">
                <h2 class="hndle">
                    <span><?php echo esc_html__('Invoice Items', 'simple-invoice'); ?></span>
                </h2>
                <div class="inside">
                    <div class="si-items-header">
                        <p><?php echo esc_html__('Add products or services to your invoice', 'simple-invoice'); ?></p>
                        <button type="button" class="button button-primary" id="si-add-item">
                            <span class="dashicons dashicons-plus-alt"></span>
                            <?php echo esc_html__('Add Item', 'simple-invoice'); ?>
                        </button>
                    </div>

                    <div class="table-responsive">
                        <table class="wp-list-table widefat fixed striped" id="si-invoice-items">
                            <thead>
                                <tr>
                                    <th scope="col" width="5%"><?php echo esc_html__('#', 'simple-invoice'); ?></th>
                                    <th scope="col"><?php echo esc_html__('Description', 'simple-invoice'); ?></th>
                                    <th scope="col" width="10%"><?php echo esc_html__('Qty', 'simple-invoice'); ?></th>
                                    <th scope="col" width="15%"><?php echo esc_html__('Rate', 'simple-invoice'); ?></th>
                                    <th scope="col" width="15%"><?php echo esc_html__('Total', 'simple-invoice'); ?></th>
                                    <th scope="col" width="8%"><?php echo esc_html__('Actions', 'simple-invoice'); ?></th>
                                </tr>
                            </thead>
                            <tbody id="si-items-body">
                                <!-- Items will be added here -->
                            </tbody>
                        </table>
                    </div>

                    <?php if (empty($invoices)): ?>
                        <div class="notice notice-warning notice-alt inline">
                            <p><?php echo esc_html__('No items added yet. Click "Add Item" to start building your invoice', 'simple-invoice'); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="postbox">
                <h2 class="hndle">
                    <span><?php echo esc_html__('Invoice Summary', 'simple-invoice'); ?></span>
                </h2>
                <div class="inside">
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php echo esc_html__('Subtotal', 'simple-invoice'); ?></th>
                            <td><span id="si-subtotal">$0.00</span></td>
                        </tr>
                        <tr>
                            <th scope="row"><?php echo esc_html__('Tax Rate (%)', 'simple-invoice'); ?></th>
                            <td>
                                <input type="number" class="small-text" id="si-tax-rate" name="tax_rate" step="0.01" min="0" value="0" />
                                <span id="si-tax-amount">$0.00</span>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php echo esc_html__('Discount', 'simple-invoice'); ?></th>
                            <td>
                                <input type="number" class="small-text" id="si-discount" name="discount" step="0.01" min="0" value="0" />
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php echo esc_html__('Shipping', 'simple-invoice'); ?></th>
                            <td>
                                <input type="number" class="small-text" id="si-shipping" name="shipping" step="0.01" min="0" value="0" />
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><strong><?php echo esc_html__('Total Amount', 'simple-invoice'); ?></strong></th>
                            <td><strong><span id="si-total-amount">$0.00</span></strong></td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="postbox">
                <h2 class="hndle">
                    <span><?php echo esc_html__('Additional Notes', 'simple-invoice'); ?></span>
                </h2>
                <div class="inside">
                    <textarea id="si-invoice-notes"
                              name="notes"
                              rows="5"
                              class="large-text"
                              placeholder="<?php echo esc_attr__('Enter any additional notes, payment terms, or special instructions for the client...', 'simple-invoice'); ?>"></textarea>
                    <p class="description">
                        <?php echo esc_html__('These notes will appear at the bottom of your invoice', 'simple-invoice'); ?>
                    </p>
                </div>
            </div>

            <div class="submitbox">
                <div id="major-publishing-actions">
                    <div id="publishing-action">
                        <button type="button" class="button button-primary button-large" id="si-create-invoice">
                            <?php echo esc_html__('Create & Download PDF', 'simple-invoice'); ?>
                        </button>
                    </div>
                    <div class="clear"></div>
                </div>
            </div>
        </form>
    <?php endif; ?>
</div>

<!-- Quick Add Client Modal -->
<div id="si-quick-client-modal" style="display:none;">
    <div class="wrap">
        <h1><?php echo esc_html__('Add New Client', 'simple-invoice'); ?></h1>
        <form id="si-quick-client-form">
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="si-quick-client-name"><?php echo esc_html__('Name', 'simple-invoice'); ?> <span class="required">*</span></label>
                    </th>
                    <td>
                        <input type="text" 
                               id="si-quick-client-name" 
                               name="name" 
                               class="regular-text" 
                               required 
                               placeholder="<?php echo esc_attr__('Client full name', 'simple-invoice'); ?>" />
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="si-quick-client-business"><?php echo esc_html__('Business Name', 'simple-invoice'); ?></label>
                    </th>
                    <td>
                        <input type="text" 
                               id="si-quick-client-business" 
                               name="business_name" 
                               class="regular-text" 
                               placeholder="<?php echo esc_attr__('Business or company name', 'simple-invoice'); ?>" />
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="si-quick-client-email"><?php echo esc_html__('Email',
<?php
/**
 * Create Invoice Page Template
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get clients and templates
$client_manager = new SI_Client();
$template_manager = new SI_Template();
$clients = $client_manager->si_get_clients();
$templates = $template_manager->si_get_templates();

// Get selected template if provided
$selected_template_id = isset($_GET['template_id']) ? intval($_GET['template_id']) : 0;
$selected_template = null;

if ($selected_template_id > 0) {
    $selected_template = $template_manager->si_get_template($selected_template_id);
}
?>

<div class="wrap">
    <h1 class="wp-heading-inline"><?php echo esc_html__('Create New Invoice', 'wp-invoice-manager-pro'); ?></h1>
    <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-invoices')); ?>" class="page-title-action">
        <span class="dashicons dashicons-list-view"></span>
        <?php echo esc_html__('View All Invoices', 'wp-invoice-manager-pro'); ?>
    </a>
    <hr class="wp-header-end">

    <?php if (empty($templates)): ?>
        <div class="notice notice-warning">
            <p><strong><?php echo esc_html__('No Invoice Layouts Found!', 'simple-invoice'); ?></strong></p>
            <p><?php echo esc_html__('You need to create at least one invoice layout before creating invoices. Layouts combine visual designs with field configurations to define how your invoices will look and what information they contain.', 'simple-invoice'); ?></p>
            <div class="si-help-note">
                <span class="dashicons dashicons-info"></span>
                <strong><?php echo esc_html__('What\'s the difference?', 'simple-invoice'); ?></strong><br>
                <span><?php echo esc_html__('• Designs = Visual appearance (colors, fonts, layout style)', 'simple-invoice'); ?></span><br>
                <span><?php echo esc_html__('• Layouts = Designs + Field configuration (what info to show)', 'simple-invoice'); ?></span>
            </div>
            <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-templates')); ?>" class="si-btn si-btn-primary">
                <span class="dashicons dashicons-plus-alt"></span>
                <?php echo esc_html__('Create Your First Layout', 'simple-invoice'); ?>
            </a>
        </div>
    <?php elseif (empty($clients)): ?>
        <div class="si-empty-state si-warning-state">
            <div class="si-empty-icon">
                <span class="dashicons dashicons-warning"></span>
            </div>
            <h3><?php echo esc_html__('No Clients Found!', 'simple-invoice'); ?></h3>
            <p><?php echo esc_html__('You need to add at least one client before creating invoices. Clients are the recipients of your invoices.', 'simple-invoice'); ?></p>
            <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-clients')); ?>" class="si-btn si-btn-primary">
                <span class="dashicons dashicons-plus-alt"></span>
                <?php echo esc_html__('Add Your First Client', 'simple-invoice'); ?>
            </a>
        </div>
    <?php else: ?>

        <form id="si-invoice-form">
            
            <div id="poststuff">
                <div id="post-body" class="metabox-holder columns-2">
                    <div id="post-body-content">
                        <div class="meta-box-sortables ui-sortable">
                            <div class="postbox">
                                <h2 class="hndle">
                                    <span><?php echo esc_html__('Invoice Configuration', 'simple-invoice'); ?></span>
                                </h2>
                                <div class="inside">

                                <table class="form-table">
                                    <tr>
                                        <th scope="row">
                                            <label for="si-template-select">
                                                <?php echo esc_html__('Invoice Layout', 'simple-invoice'); ?>
                                                <span class="description">(<?php echo esc_html__('required', 'simple-invoice'); ?>)</span>
                                            </label>
                                        </th>
                                        <td>
                            <select id="si-template-select" name="template_id" class="si-select" required>
                                <option value=""><?php echo esc_html__('Choose an invoice layout...', 'simple-invoice'); ?></option>
                                <?php foreach ($templates as $template): ?>
                                    <option value="<?php echo esc_attr($template->id); ?>"
                                            <?php selected($template->id, $selected_template_id); ?>
                                            data-design="<?php echo esc_attr($template->design); ?>">
                                        <?php echo esc_html($template->name); ?>
                                        <?php if (!empty($template->design) && $template->design !== 'classic'): ?>
                                            (<?php echo esc_html(ucwords(str_replace('-', ' ', $template->design))); ?> Design)
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <span class="si-field-help"><?php echo esc_html__('Choose the layout and field configuration for your invoice', 'simple-invoice'); ?></span>
                            <div class="si-template-preview" id="si-template-preview" >
                                <div class="si-preview-header">
                                    <span class="dashicons dashicons-visibility"></span>
                                    <span class="si-preview-title"><?php echo esc_html__('Selected Layout Preview', 'simple-invoice'); ?></span>
                                </div>
                                <div class="si-preview-content">
                                    <div class="si-preview-design">
                                        <span class="si-design-badge" id="si-design-badge"></span>
                                    </div>
                                    <div class="si-preview-info">
                                        <h4 id="si-template-name"></h4>
                                        <p id="si-template-description"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="si-form-field">
                        <label for="si-client-select" class="si-field-label">
                            <span class="dashicons dashicons-businessperson"></span>
                            <?php echo esc_html__('Client', 'simple-invoice'); ?>
                            <span class="required">*</span>
                        </label>
                        <div class="si-field-wrapper">
                                    <div class="si-client-select-wrapper">
                                        <select id="si-client-select" name="client_id" class="regular-text" required>
                                    <option value=""><?php echo esc_html__('Choose a client...', 'simple-invoice'); ?></option>
                                    <?php foreach ($clients as $client): ?>
                                        <option value="<?php echo esc_attr($client->id); ?>">
                                            <?php echo esc_html($client->name); ?>
                                            <?php if (!empty($client->business_name)): ?>
                                                (<?php echo esc_html($client->business_name); ?>)
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <button type="button" class="button thickbox" title="<?php echo esc_attr__('Add New Client', 'simple-invoice'); ?>" 
                                    onclick="tb_show('<?php echo esc_js(__('Add New Client', 'simple-invoice')); ?>', '#TB_inline?width=600&height=550&inlineId=si-quick-client-modal');">
                                    <span class="dashicons dashicons-plus-alt"></span>
                                </button>
                            </div>
                            <p class="description"><?php echo esc_html__('Select the client who will receive this invoice', 'simple-invoice'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="si-invoice-number">
                                <?php echo esc_html__('Invoice Number', 'simple-invoice'); ?>
                            </label>
                        </th>
                        <td>
                            <input type="text"
                                   id="si-invoice-number"
                                   name="invoice_number"
                                   class="si-input"
                                   placeholder="<?php echo esc_attr__('Auto-generated if empty', 'simple-invoice'); ?>" />
                            <p class="description"><?php echo esc_html__('Leave empty to auto-generate invoice number', 'simple-invoice'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="si-invoice-date">
                                <?php echo esc_html__('Invoice Date', 'simple-invoice'); ?>
                            </label>
                        </th>
                        <td>
                            <input type="date"
                                   id="si-invoice-date"
                                   name="invoice_date"
                                   value="<?php echo esc_attr(date('Y-m-d')); ?>"
                                   class="si-input" />
                            <span class="si-field-help"><?php echo esc_html__('Date when the invoice is issued', 'simple-invoice'); ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="postbox">
                <h2 class="hndle">
                    <span><?php echo esc_html__('Invoice Items', 'simple-invoice'); ?></span>
                </h2>
                <div class="inside">
                    <div class="si-items-header">
                        <p><?php echo esc_html__('Add products or services to your invoice', 'simple-invoice'); ?></p>
                        <button type="button" class="button button-primary" id="si-add-item">
                            <span class="dashicons dashicons-plus-alt"></span>
                            <?php echo esc_html__('Add Item', 'simple-invoice'); ?>
                        </button>
                    </div>

                    <div class="table-responsive">
                        <table class="wp-list-table widefat fixed striped" id="si-invoice-items">
                            <thead>
                                <tr>
                                    <th scope="col" width="5%"><?php echo esc_html__('#', 'simple-invoice'); ?></th>
                                    <th scope="col"><?php echo esc_html__('Description', 'simple-invoice'); ?></th>
                                    <th scope="col" width="10%"><?php echo esc_html__('Qty', 'simple-invoice'); ?></th>
                                    <th scope="col" width="15%"><?php echo esc_html__('Rate', 'simple-invoice'); ?></th>
                                    <th scope="col" width="15%"><?php echo esc_html__('Total', 'simple-invoice'); ?></th>
                                    <th scope="col" width="8%"><?php echo esc_html__('Actions', 'simple-invoice'); ?></th>
                                </tr>
                            </thead>
                            <tbody id="si-items-body">
                                <!-- Items will be added here -->
                            </tbody>
                        </table>
                    </div>

                    <?php if (empty($invoices)): ?>
                        <div class="notice notice-warning notice-alt inline">
                            <p><?php echo esc_html__('No items added yet. Click "Add Item" to start building your invoice', 'simple-invoice'); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="postbox">
                <h2 class="hndle">
                    <span><?php echo esc_html__('Invoice Summary', 'simple-invoice'); ?></span>
                </h2>
                <div class="inside">
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php echo esc_html__('Subtotal', 'simple-invoice'); ?></th>
                            <td><span id="si-subtotal">$0.00</span></td>
                        </tr>
                        <tr>
                            <th scope="row"><?php echo esc_html__('Tax Rate (%)', 'simple-invoice'); ?></th>
                            <td>
                                <input type="number" class="small-text" id="si-tax-rate" name="tax_rate" step="0.01" min="0" value="0" />
                                <span id="si-tax-amount">$0.00</span>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php echo esc_html__('Discount', 'simple-invoice'); ?></th>
                            <td>
                                <input type="number" class="small-text" id="si-discount" name="discount" step="0.01" min="0" value="0" />
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php echo esc_html__('Shipping', 'simple-invoice'); ?></th>
                            <td>
                                <input type="number" class="small-text" id="si-shipping" name="shipping" step="0.01" min="0" value="0" />
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><strong><?php echo esc_html__('Total Amount', 'simple-invoice'); ?></strong></th>
                            <td><strong><span id="si-total-amount">$0.00</span></strong></td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="postbox">
                <h2 class="hndle">
                    <span><?php echo esc_html__('Additional Notes', 'simple-invoice'); ?></span>
                </h2>
                <div class="inside">
                    <textarea id="si-invoice-notes"
                              name="notes"
                              rows="5"
                              class="large-text"
                              placeholder="<?php echo esc_attr__('Enter any additional notes, payment terms, or special instructions for the client...', 'simple-invoice'); ?>"></textarea>
                    <p class="description">
                        <?php echo esc_html__('These notes will appear at the bottom of your invoice', 'simple-invoice'); ?>
                    </p>
                </div>
            </div>

            <div class="submitbox">
                <div id="major-publishing-actions">
                    <div id="publishing-action">
                        <button type="button" class="button button-primary button-large" id="si-create-invoice">
                            <?php echo esc_html__('Create & Download PDF', 'simple-invoice'); ?>
                        </button>
                    </div>
                    <div class="clear"></div>
                </div>
            </div>
        </form>
    <?php endif; ?>
</div>

<!-- Quick Add Client Modal -->
<div id="si-quick-client-modal" class="si-modal" >
    <div class="si-modal-content">
        <div class="si-modal-header">
            <h2><?php echo esc_html__('Add New Client', 'simple-invoice'); ?></h2>
            <button type="button" class="si-modal-close">&times;</button>
        </div>
        
        <div class="si-modal-body">
            <form id="si-quick-client-form">
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="si-quick-client-name"><?php echo esc_html__('Name', 'simple-invoice'); ?> <span class="required">*</span></label>
                        </th>
                        <td>
                            <input type="text" 
                                   id="si-quick-client-name" 
                                   name="name" 
                                   class="regular-text" 
                                   required 
                                   placeholder="<?php echo esc_attr__('Client full name', 'simple-invoice'); ?>" />
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="si-quick-client-business"><?php echo esc_html__('Business Name', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="text" 
                                   id="si-quick-client-business" 
                                   name="business_name" 
                                   class="regular-text" 
                                   placeholder="<?php echo esc_attr__('Business or company name', 'simple-invoice'); ?>" />
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="si-quick-client-email"><?php echo esc_html__('Email', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="email" 
                                   id="si-quick-client-email" 
                                   name="email" 
                                   class="regular-text" 
                                   placeholder="<?php echo esc_attr__('<EMAIL>', 'simple-invoice'); ?>" />
                        </td>
                    </tr>
                </table>
            </form>
        </div>
        
        <div class="si-modal-footer">
            <button type="button" class="button button-secondary si-modal-close"><?php echo esc_html__('Cancel', 'simple-invoice'); ?></button>
            <button type="button" class="button button-primary" id="si-save-quick-client"><?php echo esc_html__('Add Client', 'simple-invoice'); ?></button>
        </div>
    </div>
</div>

<!-- Invoice Preview Modal -->
<div id="si-preview-modal" class="si-modal si-modal-large" >
    <div class="si-modal-content">
        <div class="si-modal-header">
            <h2><?php echo esc_html__('Invoice Preview', 'simple-invoice'); ?></h2>
            <button type="button" class="si-modal-close">&times;</button>
        </div>
        
        <div class="si-modal-body">
            <div id="si-preview-content">
                <!-- Preview content will be loaded here -->
            </div>
        </div>
        
        <div class="si-modal-footer">
            <button type="button" class="button button-secondary si-modal-close"><?php echo esc_html__('Close', 'simple-invoice'); ?></button>
            <button type="button" class="button button-primary" id="si-create-from-preview">
                <?php echo esc_html__('Create & Download PDF', 'simple-invoice'); ?>
            </button>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    var itemCounter = 0;

    // Add invoice item
    $('#si-add-item').on('click', function() {
        addInvoiceItem();
    });

    // Remove invoice item
    $(document).on('click', '.si-remove-item', function() {
        $(this).closest('tr').remove();
        updateSerialNumbers();
        calculateTotals();
        updateEmptyState();
    });

    // Calculate totals on input change
    $(document).on('input', '.si-item-quantity, .si-item-rate, .si-calc-input', function() {
        calculateTotals();
    });

    // Quick add client
    $('.si-add-client-quick').on('click', function() {
        $('#si-quick-client-modal').show();
    });

    // Save quick client
    $('#si-save-quick-client').on('click', function() {
        saveQuickClient();
    });

    // Preview invoice
    $('#si-preview-invoice').on('click', function() {
        if (validateForm()) {
            previewInvoice();
        }
    });

    // Create invoice
    $('#si-create-invoice, #si-create-from-preview').on('click', function() {
        if (validateForm()) {
            createInvoice();
        }
    });

    // Close modals
    $('.si-modal-close').on('click', function() {
        $(this).closest('.si-modal').hide();
    });

    // Template selection preview
    $('#si-template-select').on('change', function() {
        var selectedOption = $(this).find('option:selected');
        var templateName = selectedOption.text();
        var designName = selectedOption.data('design') || 'classic';

        if ($(this).val()) {
            $('#si-template-name').text(templateName);
            $('#si-design-badge').text(designName.charAt(0).toUpperCase() + designName.slice(1) + ' Design');
            $('#si-template-description').text('<?php echo esc_js(__('This layout will be used to generate your invoice with the selected design and field configuration.', 'simple-invoice')); ?>');
            $('#si-template-preview').slideDown(300);
        } else {
            $('#si-template-preview').slideUp(300);
        }
    });

    function addInvoiceItem() {
        itemCounter++;
        var rowHtml = '<tr data-item-index="' + itemCounter + '">';

        // Serial number
        rowHtml += '<td><span class="si-item-serial">' + itemCounter + '</span></td>';

        // Description
        rowHtml += '<td><input type="text" class="si-item-description" name="items[' + itemCounter + '][description]" placeholder="<?php echo esc_attr__('Enter item description...', 'simple-invoice'); ?>" required /></td>';

        // Quantity
        rowHtml += '<td><input type="number" class="si-item-quantity" name="items[' + itemCounter + '][quantity]" step="1" min="1" value="1" required /></td>';

        // Rate
        rowHtml += '<td><input type="number" class="si-item-rate" name="items[' + itemCounter + '][rate]" step="0.01" min="0" placeholder="0.00" required /></td>';

        // Total (calculated)
        rowHtml += '<td><span class="si-item-total">$0.00</span></td>';

        // Actions
        rowHtml += '<td><button type="button" class="si-remove-item" title="<?php echo esc_attr__('Remove this item', 'simple-invoice'); ?>"><span class="dashicons dashicons-trash"></span></button></td>';

        rowHtml += '</tr>';

        $('#si-items-body').append(rowHtml);
        updateEmptyState();
        calculateTotals();

        // Focus on the description field of the new item
        $('#si-items-body tr:last-child .si-item-description').focus();
    }

    function updateSerialNumbers() {
        $('#si-items-body tr').each(function(index) {
            $(this).find('.si-item-serial').text(index + 1);
            $(this).attr('data-item-index', index + 1);
        });
        itemCounter = $('#si-items-body tr').length;
    }

    function updateEmptyState() {
        var hasItems = $('#si-items-body tr').length > 0;
        if (hasItems) {
            $('#si-items-empty').hide();
            $('#si-invoice-items').show();
        } else {
            $('#si-items-empty').show();
            $('#si-invoice-items').hide();
        }
    }

    function validateForm() {
        var isValid = true;
        var errors = [];

        // Check if client is selected
        if (!$('#si-client-select').val()) {
            errors.push('<?php echo esc_js(__('Please select a client.', 'simple-invoice')); ?>');
            isValid = false;
        }

        // Check if template is selected
        if (!$('#si-template-select').val()) {
            errors.push('<?php echo esc_js(__('Please select a template.', 'simple-invoice')); ?>');
            isValid = false;
        }

        // Check if there are items
        if ($('#si-items-body tr').length === 0) {
            errors.push('<?php echo esc_js(__('Please add at least one item.', 'simple-invoice')); ?>');
            isValid = false;
        }

        // Validate item fields
        var hasEmptyItems = false;
        $('#si-items-body tr').each(function() {
            var description = $(this).find('.si-item-description').val().trim();
            var quantity = $(this).find('.si-item-quantity').val();
            var rate = $(this).find('.si-item-rate').val();

            if (!description || !quantity || !rate) {
                hasEmptyItems = true;
            }
        });

        if (hasEmptyItems) {
            errors.push('<?php echo esc_js(__('Please fill in all item fields.', 'simple-invoice')); ?>');
            isValid = false;
        }

        if (!isValid) {
            alert(errors.join('\\n'));
        }

        return isValid;
    }
    
    function calculateTotals() {
        var subtotal = 0;

        // Calculate item totals
        $('#si-items-body tr').each(function() {
            var row = $(this);
            var quantity = parseFloat(row.find('.si-item-quantity').val()) || 0;
            var rate = parseFloat(row.find('.si-item-rate').val()) || 0;
            var total = quantity * rate;

            row.find('.si-item-total').text('$' + total.toFixed(2));
            subtotal += total;
        });

        // Update subtotal
        $('#si-subtotal').text('$' + subtotal.toFixed(2));

        // Get tax, discount, and shipping values
        var taxRate = parseFloat($('#si-tax-rate').val()) || 0;
        var discount = parseFloat($('#si-discount').val()) || 0;
        var shipping = parseFloat($('#si-shipping').val()) || 0;

        // Calculate tax amount
        var taxAmount = subtotal * (taxRate / 100);
        $('#si-tax-amount').text('$' + taxAmount.toFixed(2));

        // Calculate final total
        var finalTotal = subtotal + taxAmount - discount + shipping;
        $('#si-total-amount').text(finalTotal.toFixed(2));

        // Update total card styling based on amount
        var totalCard = $('.si-total-card');
        if (finalTotal > 0) {
            totalCard.removeClass('si-total-zero');
        } else {
            totalCard.addClass('si-total-zero');
        }
    }
    
    function saveQuickClient() {
        var formData = $('#si-quick-client-form').serialize();
        formData += '&action=si_add_client&nonce=<?php echo wp_create_nonce('si_add_client_nonce'); ?>';
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success && response.data.client) {
                    var client = response.data.client;
                    var optionText = client.name;
                    if (client.business_name) {
                        optionText += ' (' + client.business_name + ')';
                    }
                    
                    $('#si-client-select').append('<option value="' + client.id + '">' + optionText + '</option>');
                    $('#si-client-select').val(client.id);
                    $('#si-quick-client-modal').hide();
                    $('#si-quick-client-form')[0].reset();
                } else {
                    alert(response.message || '<?php echo esc_js(__('Failed to add client.', 'simple-invoice')); ?>');
                }
            }
        });
    }
    
    function previewInvoice() {
        var formData = collectInvoiceData();

        // Show loading state
        $('#si-preview-invoice').prop('disabled', true).text('<?php echo esc_js(__('Loading...', 'simple-invoice')); ?>');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'si_preview_invoice',
                client_id: formData.client_id,
                template_id: formData.template_id,
                invoice_data: formData.invoice_data,
                nonce: '<?php echo wp_create_nonce('si_preview_invoice_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    $('#si-preview-content').html(response.data.html_content);
                    $('#si-preview-modal').show();
                } else {
                    alert(response.data || '<?php echo esc_js(__('Failed to generate preview.', 'simple-invoice')); ?>');
                }
            },
            error: function(xhr, status, error) {
                alert('<?php echo esc_js(__('An error occurred while generating preview.', 'simple-invoice')); ?>');
            },
            complete: function() {
                // Reset button state
                $('#si-preview-invoice').prop('disabled', false).text('<?php echo esc_js(__('Preview Invoice', 'simple-invoice')); ?>');
            }
        });
    }
    
    function createInvoice() {
        var formData = collectInvoiceData();

        // Show loading state
        $('#si-create-invoice, #si-create-from-preview').prop('disabled', true).text('<?php echo esc_js(__('Creating...', 'simple-invoice')); ?>');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'si_create_invoice',
                client_id: formData.client_id,
                template_id: formData.template_id,
                invoice_number: formData.invoice_number,
                invoice_data: formData.invoice_data,
                nonce: '<?php echo wp_create_nonce('si_create_invoice_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    alert(response.data.message || '<?php echo esc_js(__('Invoice created successfully!', 'simple-invoice')); ?>');

                    // Open download URL in new window/tab
                    window.open(response.data.download_url, '_blank');

                    // Reset form after successful creation
                    $('#si-invoice-form')[0].reset();
                    $('#si-items-body').empty();
                    addInvoiceItem(); // Add one default item
                    calculateTotals();
                } else {
                    alert(response.data || '<?php echo esc_js(__('Failed to create invoice.', 'simple-invoice')); ?>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                alert('<?php echo esc_js(__('An error occurred. Please try again.', 'simple-invoice')); ?>');
            },
            complete: function() {
                // Reset button state
                $('#si-create-invoice, #si-create-from-preview').prop('disabled', false).text('<?php echo esc_js(__('Create & Download PDF', 'simple-invoice')); ?>');
            }
        });
    }
    
    function collectInvoiceData() {
        var items = [];

        // Collect items
        $('#si-items-body tr').each(function() {
            var row = $(this);
            var item = {
                description: row.find('.si-item-description').val(),
                quantity: parseFloat(row.find('.si-item-quantity').val()) || 0,
                rate: parseFloat(row.find('.si-item-rate').val()) || 0
            };

            if (item.description && item.quantity > 0 && item.rate >= 0) {
                items.push(item);
            }
        });

        var formData = {
            client_id: $('#si-client-select').val(),
            template_id: $('#si-template-select').val(),
            invoice_number: $('#si-invoice-number').val(),
            invoice_data: {
                items: items,
                tax_rate: parseFloat($('#si-tax-rate').val()) || 0,
                discount: parseFloat($('#si-discount').val()) || 0,
                shipping: parseFloat($('#si-shipping').val()) || 0,
                notes: $('#si-invoice-notes').val()
            }
        };

        return formData;
    }

    // Initialize page
    $(document).ready(function() {
        updateEmptyState();
        calculateTotals();
    });
});
</script>
