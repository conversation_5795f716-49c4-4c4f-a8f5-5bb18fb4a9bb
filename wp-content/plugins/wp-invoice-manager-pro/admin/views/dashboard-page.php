<?php
/**
 * Dashboard Page Template
 *
 * @package WPInvoiceManagerPro
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get managers
$invoice_manager = new SI_Invoice();
$client_manager = new SI_Client();

// Get statistics
$stats = array(
    'total' => $invoice_manager->si_count_invoices_with_filters('', ''),
    'draft' => $invoice_manager->si_count_invoices_with_filters('', 'draft'),
    'sent' => $invoice_manager->si_count_invoices_with_filters('', 'sent'),
    'paid' => $invoice_manager->si_count_invoices_with_filters('', 'paid'),
    'overdue' => $invoice_manager->si_count_invoices_with_filters('', 'overdue')
);

// Calculate total revenue (paid invoices)
$paid_invoices = $invoice_manager->si_get_invoices(array('status' => 'paid', 'limit' => -1));
$total_revenue = 0;
foreach ($paid_invoices as $invoice) {
    $total_revenue += floatval($invoice->total_amount);
}

// Get recent invoices (last 5)
$recent_invoices = $invoice_manager->si_get_invoices(array('limit' => 5));

// Get total clients
$total_clients = count($client_manager->si_get_clients());

// Get current month stats
$current_month = date('Y-m');
$monthly_invoices = $invoice_manager->si_get_invoices_with_filters('', '', $current_month . '-01', $current_month . '-31', -1, 0);
$monthly_revenue = 0;
foreach ($monthly_invoices as $invoice) {
    if ($invoice->status === 'paid') {
        $monthly_revenue += floatval($invoice->total_amount);
    }
}
?>

<div class="wim-pro-admin-wrapper">
    <div class="page-header">
        <h1><?php echo esc_html__('Dashboard', 'wp-invoice-manager-pro'); ?></h1>
        <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-create-invoice')); ?>" class="button button-primary">
            <?php echo esc_html__('Create Invoice', 'wp-invoice-manager-pro'); ?>
        </a>
    </div>

    <!-- Statistics Overview -->
    <div id="dashboard-widgets-wrap">
        <div id="dashboard-widgets" class="metabox-holder">
            <div id="postbox-container-1" class="postbox-container">
                <div class="meta-box-sortables">
                    <div class="postbox">
                        <h2 class="hndle"><span><?php echo esc_html__('Overview', 'wp-invoice-manager-pro'); ?></span></h2>
                        <div class="inside">
                            <div class="main">
                                <ul>
                                    <li>
                                        <span class="dashicons dashicons-chart-line"></span>
                                        <strong><?php echo esc_html(si_format_currency($total_revenue)); ?></strong>
                                        <?php echo esc_html__('Total Revenue', 'wp-invoice-manager-pro'); ?>
                                    </li>
                                    <li>
                                        <span class="dashicons dashicons-calendar-alt"></span>
                                        <strong><?php echo esc_html(si_format_currency($monthly_revenue)); ?></strong>
                                        <?php echo esc_html__('This Month', 'wp-invoice-manager-pro'); ?>
                                    </li>
                                    <li>
                                        <span class="dashicons dashicons-media-text"></span>
                                        <strong><?php echo esc_html($stats['total']); ?></strong>
                                        <?php echo esc_html__('Total Invoices', 'wp-invoice-manager-pro'); ?>
                                    </li>
                                    <li>
                                        <span class="dashicons dashicons-groups"></span>
                                        <strong><?php echo esc_html($total_clients); ?></strong>
                                        <?php echo esc_html__('Total Clients', 'wp-invoice-manager-pro'); ?>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="postbox">
                        <h2 class="hndle"><span><?php echo esc_html__('Invoice Status', 'wp-invoice-manager-pro'); ?></span></h2>
                        <div class="inside">
                            <div class="main">
                                <ul>
                                    <li><span class="dashicons dashicons-yes-alt"></span><strong><?php echo esc_html($stats['paid']); ?></strong> <?php echo esc_html__('Paid', 'wp-invoice-manager-pro'); ?></li>
                                    <li><span class="dashicons dashicons-marker"></span><strong><?php echo esc_html($stats['sent']); ?></strong> <?php echo esc_html__('Sent', 'wp-invoice-manager-pro'); ?></li>
                                    <li><span class="dashicons dashicons-edit"></span><strong><?php echo esc_html($stats['draft']); ?></strong> <?php echo esc_html__('Draft', 'wp-invoice-manager-pro'); ?></li>
                                    <li><span class="dashicons dashicons-warning"></span><strong><?php echo esc_html($stats['overdue']); ?></strong> <?php echo esc_html__('Overdue', 'wp-invoice-manager-pro'); ?></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="postbox-container-2" class="postbox-container">
                <div class="meta-box-sortables">
                    <div class="postbox">
                        <h2 class="hndle"><span><?php echo esc_html__('Recent Invoices', 'wp-invoice-manager-pro'); ?></span></h2>
                        <div class="inside">
                            <table class="wp-list-table widefat fixed striped">
                                <thead>
                                    <tr>
                                        <th><?php echo esc_html__('Invoice #', 'wp-invoice-manager-pro'); ?></th>
                                        <th><?php echo esc_html__('Client', 'wp-invoice-manager-pro'); ?></th>
                                        <th><?php echo esc_html__('Amount', 'wp-invoice-manager-pro'); ?></th>
                                        <th><?php echo esc_html__('Status', 'wp-invoice-manager-pro'); ?></th>
                                        <th><?php echo esc_html__('Date', 'wp-invoice-manager-pro'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($recent_invoices)) : ?>
                                        <?php foreach ($recent_invoices as $invoice) : ?>
                                            <?php
                                            $client = $client_manager->si_get_client($invoice->client_id);
                                            $client_name = $client ? $client->name : __('Unknown Client', 'wp-invoice-manager-pro');
                                            ?>
                                            <tr>
                                                <td><a href="<?php echo esc_url(admin_url('admin.php?page=wimp-create-invoice&invoice_id=' . $invoice->id)); ?>"><?php echo esc_html($invoice->invoice_number); ?></a></td>
                                                <td><?php echo esc_html($client_name); ?></td>
                                                <td><?php echo esc_html(si_format_currency($invoice->total_amount)); ?></td>
                                                <td><span class="wim-status-<?php echo esc_attr($invoice->status); ?>"><?php echo esc_html(ucfirst($invoice->status)); ?></span></td>
                                                <td><?php echo esc_html(date_i18n(get_option('date_format'), strtotime($invoice->created_at))); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else : ?>
                                        <tr>
                                            <td colspan="5"><?php echo esc_html__('No recent invoices found.', 'wp-invoice-manager-pro'); ?></td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<?php
// Include common footer
include WIMP_PLUGIN_PATH . 'admin/views/common/page-footer.php';
?>
