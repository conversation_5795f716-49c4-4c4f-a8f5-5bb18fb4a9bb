<?php
/**
 * Invoices List Page Template
 *
 * @package WPInvoiceManagerPro
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get managers
$invoice_manager = new SI_Invoice();
$client_manager = new SI_Client();

// Handle search and pagination
$search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';
$paged = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$per_page = 20;
$offset = ($paged - 1) * $per_page;

// Get invoices with filters
$invoices = $invoice_manager->si_get_invoices_with_filters($search, $status_filter, null, null, $per_page, $offset);
$total_invoices = $invoice_manager->si_count_invoices_with_filters($search, $status_filter);
$total_pages = ceil($total_invoices / $per_page);

// Set up page header variables
$page_title = __('All Invoices', 'wp-invoice-manager-pro');
$page_subtitle = __('Manage and track all your invoices', 'wp-invoice-manager-pro');
$page_icon = 'dashicons-media-text';
$header_actions = array(
    array(
        'type' => 'link',
        'url' => admin_url('admin.php?page=wimp-create-invoice'),
        'text' => __('Add New Invoice', 'wp-invoice-manager-pro'),
        'icon' => 'dashicons-plus-alt',
        'class' => 'si-btn si-btn-primary'
    )
);

// Include common header
include WIMP_PLUGIN_PATH . 'admin/views/common/page-header.php';
?>

    <div class="wp-list-table-top">
        <form method="get">
            <input type="hidden" name="page" value="wimp-invoices" />
            <div class="search-box">
                <label class="screen-reader-text" for="invoice-search-input"><?php echo esc_html__('Search Invoices:', 'wp-invoice-manager-pro'); ?></label>
                <input type="search" id="invoice-search-input" name="search" value="<?php echo esc_attr($search); ?>">
                <input type="submit" id="search-submit" class="button" value="<?php echo esc_attr__('Search Invoices', 'wp-invoice-manager-pro'); ?>">
            </div>
        </form>
        <div class="tablenav top">
            <div class="alignleft actions">
                <form method="get">
                    <input type="hidden" name="page" value="wimp-invoices" />
                    <select name="status">
                        <option value=""><?php echo esc_html__('All Statuses', 'wp-invoice-manager-pro'); ?></option>
                        <option value="draft" <?php selected($status_filter, 'draft'); ?>><?php echo esc_html__('Draft', 'wp-invoice-manager-pro'); ?></option>
                        <option value="sent" <?php selected($status_filter, 'sent'); ?>><?php echo esc_html__('Sent', 'wp-invoice-manager-pro'); ?></option>
                        <option value="paid" <?php selected($status_filter, 'paid'); ?>><?php echo esc_html__('Paid', 'wp-invoice-manager-pro'); ?></option>
                        <option value="overdue" <?php selected($status_filter, 'overdue'); ?>><?php echo esc_html__('Overdue', 'wp-invoice-manager-pro'); ?></option>
                    </select>
                    <input type="submit" class="button" value="<?php echo esc_attr__('Filter', 'wp-invoice-manager-pro'); ?>">
                </form>
            </div>
            <?php if ($total_pages > 1) : ?>
                <div class="tablenav-pages">
                    <span class="displaying-num"><?php echo esc_html($total_invoices); ?> <?php echo esc_html__('items', 'wp-invoice-manager-pro'); ?></span>
                    <span class="pagination-links">
                        <?php
                        echo paginate_links(array(
                            'base' => add_query_arg('paged', '%#%'),
                            'format' => '',
                            'prev_text' => __('&laquo;'),
                            'next_text' => __('&raquo;'),
                            'total' => $total_pages,
                            'current' => $paged,
                        ));
                        ?>
                    </span>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <table class="wp-list-table widefat fixed striped">
        <thead>
            <tr>
                <th scope="col" class="manage-column column-primary"><?php echo esc_html__('Invoice #', 'wp-invoice-manager-pro'); ?></th>
                <th scope="col" class="manage-column"><?php echo esc_html__('Client', 'wp-invoice-manager-pro'); ?></th>
                <th scope="col" class="manage-column"><?php echo esc_html__('Amount', 'wp-invoice-manager-pro'); ?></th>
                <th scope="col" class="manage-column"><?php echo esc_html__('Status', 'wp-invoice-manager-pro'); ?></th>
                <th scope="col" class="manage-column"><?php echo esc_html__('Date', 'wp-invoice-manager-pro'); ?></th>
                <th scope="col" class="manage-column"><?php echo esc_html__('Actions', 'wp-invoice-manager-pro'); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($invoices)) : ?>
                <?php foreach ($invoices as $invoice) : ?>
                    <?php
                    $client = $client_manager->si_get_client($invoice->client_id);
                    $client_name = $client ? $client->name : __('Unknown Client', 'wp-invoice-manager-pro');
                    ?>
                    <tr>
                        <td class="column-primary">
                            <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-create-invoice&invoice_id=' . $invoice->id)); ?>">
                                <strong><?php echo esc_html($invoice->invoice_number); ?></strong>
                            </a>
                        </td>
                        <td><?php echo esc_html($client_name); ?></td>
                        <td><?php echo esc_html(si_format_currency($invoice->total_amount)); ?></td>
                        <td><span class="wim-status-<?php echo esc_attr($invoice->status); ?>"><?php echo esc_html(ucfirst($invoice->status)); ?></span></td>
                        <td><?php echo esc_html(date_i18n(get_option('date_format'), strtotime($invoice->created_at))); ?></td>
                        <td>
                            <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-create-invoice&invoice_id=' . $invoice->id)); ?>" class="button button-secondary"><?php echo esc_html__('Edit', 'wp-invoice-manager-pro'); ?></a>
                            <a href="<?php echo esc_url(wp_nonce_url(add_query_arg(array('action' => 'wim_delete_invoice', 'invoice_id' => $invoice->id), admin_url('admin.php?page=wimp-invoices')), 'wim_delete_invoice_nonce', 'wim_nonce')); ?>" class="button button-link-delete"><?php echo esc_html__('Delete', 'wp-invoice-manager-pro'); ?></a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php else : ?>
                <tr>
                    <td colspan="6"><?php echo esc_html__('No invoices found.', 'wp-invoice-manager-pro'); ?></td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>

<?php
// Include common footer
include WIMP_PLUGIN_PATH . 'admin/views/common/page-footer.php';
?>
