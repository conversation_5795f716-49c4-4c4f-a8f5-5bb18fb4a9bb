<?php
/**
 * Invoice Management Class
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * SI_Invoice class for managing invoices and PDF generation
 *
 * @since 1.0.0
 */
class SI_Invoice {

    /**
     * Table name
     *
     * @var string
     * @since 1.0.0
     */
    private $table_name;

    /**
     * Design loader instance
     *
     * @var SI_Design_Loader
     * @since 1.0.0
     */
    private $design_loader;

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'si_invoices';

        // Initialize hooks
        $this->si_init_hooks();
    }

    /**
     * Initialize hooks
     *
     * @since 1.0.0
     */
    private function si_init_hooks() {
        add_action('wp_ajax_si_create_invoice', array($this, 'si_ajax_create_invoice'));
        add_action('wp_ajax_si_generate_pdf', array($this, 'si_ajax_generate_pdf'));
        add_action('wp_ajax_si_preview_invoice', array($this, 'si_ajax_preview_invoice'));
        add_action('wp_ajax_si_view_invoice', array($this, 'si_ajax_view_invoice'));
        add_action('wp_ajax_si_update_invoice_status', array($this, 'si_ajax_update_invoice_status'));
        add_action('wp_ajax_si_delete_invoice', array($this, 'si_ajax_delete_invoice'));

        // Handle PDF downloads early in the WordPress initialization process
        add_action('init', array($this, 'si_handle_pdf_download'), 1);

        // Also handle PDF downloads on admin_init for admin area
        add_action('admin_init', array($this, 'si_handle_pdf_download'), 1);
    }

    /**
     * Get design loader instance
     *
     * @return SI_Design_Loader
     * @since 1.0.0
     */
    private function si_get_design_loader() {
        if (!$this->design_loader) {
            $this->design_loader = new SI_Design_Loader();
        }
        return $this->design_loader;
    }

    /**
     * Create a new invoice
     *
     * @param array $data Invoice data
     * @return int|false Invoice ID on success, false on failure
     * @since 1.0.0
     */
    public function si_create_invoice($data) {
        global $wpdb;

        // Debug: Log input data
        error_log('SI Create Invoice - Input data: ' . print_r($data, true));

        // Sanitize and validate data
        $sanitized_data = $this->si_sanitize_invoice_data($data);
        error_log('SI Create Invoice - Sanitized data: ' . print_r($sanitized_data, true));

        if (!$this->si_validate_invoice_data($sanitized_data)) {
            error_log('SI Create Invoice - Validation failed');
            return false;
        }

        // Generate unique invoice number if not provided
        if (empty($sanitized_data['invoice_number'])) {
            $sanitized_data['invoice_number'] = si_generate_invoice_number();
        }

        // Calculate total amount
        $sanitized_data['total_amount'] = $this->si_calculate_total($sanitized_data['invoice_data']);
        error_log('SI Create Invoice - Total amount: ' . $sanitized_data['total_amount']);

        // Insert invoice
        $result = $wpdb->insert(
            $this->table_name,
            array(
                'invoice_number' => $sanitized_data['invoice_number'],
                'client_id' => $sanitized_data['client_id'],
                'template_id' => $sanitized_data['template_id'],
                'invoice_data' => wp_json_encode($sanitized_data['invoice_data']),
                'total_amount' => $sanitized_data['total_amount'],
                'status' => $sanitized_data['status']
            ),
            array('%s', '%d', '%d', '%s', '%f', '%s')
        );

        if ($result) {
            error_log('SI Create Invoice - Success, ID: ' . $wpdb->insert_id);
            return $wpdb->insert_id;
        } else {
            error_log('SI Create Invoice - Database error: ' . $wpdb->last_error);
            return false;
        }
    }

    /**
     * Generate PDF for invoice
     *
     * @param int $invoice_id Invoice ID
     * @param bool $download Whether to force download
     * @return string|false PDF content or false on failure
     * @since 1.0.0
     */
    public function si_generate_pdf($invoice_id, $download = false) {
        $invoice = $this->si_get_invoice($invoice_id);

        if (!$invoice) {
            return false;
        }

        // Get related data
        $client_manager = new SI_Client();
        $template_manager = new SI_Template();

        $client = $client_manager->si_get_client($invoice->client_id);
        $template = $template_manager->si_get_template($invoice->template_id);
        $settings = si_get_settings();

        if (!$client || !$template) {
            return false;
        }

        // Prepare data for template
        $template_data = $this->si_prepare_template_data($invoice, $client, $template, $settings);

        // For PDF generation, use template data directly instead of parsing HTML
        if ($download) {
            return $this->si_create_pdf_from_data($template_data, $invoice->invoice_number);
        }

        // Load design template for preview
        $html_content = $this->si_get_design_loader()->si_load_design_template(
            $template->design ?? 'classic',
            $template_data
        );

        // Generate PDF using available libraries or fallback method
        return $this->si_create_pdf($html_content, $invoice->invoice_number, $download);
    }

    /**
     * Get invoice by ID
     *
     * @param int $invoice_id Invoice ID
     * @return object|null Invoice object or null if not found
     * @since 1.0.0
     */
    public function si_get_invoice($invoice_id) {
        global $wpdb;

        $invoice = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $this->table_name WHERE id = %d",
                $invoice_id
            )
        );

        if ($invoice) {
            $invoice->invoice_data = json_decode($invoice->invoice_data, true) ?: array();
        }

        return $invoice;
    }

    /**
     * Get all invoices
     *
     * @param array $args Query arguments
     * @return array Array of invoice objects
     * @since 1.0.0
     */
    public function si_get_invoices($args = array()) {
        global $wpdb;

        $defaults = array(
            'client_id' => 0,
            'status' => '',
            'orderby' => 'created_at',
            'order' => 'DESC',
            'limit' => -1,
            'offset' => 0
        );

        $args = wp_parse_args($args, $defaults);

        $sql = "SELECT * FROM $this->table_name";
        $where_conditions = array();

        // Add client filter
        if ($args['client_id'] > 0) {
            $where_conditions[] = $wpdb->prepare("client_id = %d", $args['client_id']);
        }

        // Add status filter
        if (!empty($args['status'])) {
            $where_conditions[] = $wpdb->prepare("status = %s", $args['status']);
        }

        // Add WHERE clause if conditions exist
        if (!empty($where_conditions)) {
            $sql .= " WHERE " . implode(' AND ', $where_conditions);
        }

        // Add ORDER BY clause
        $sql .= " ORDER BY " . esc_sql($args['orderby']) . " " . esc_sql($args['order']);

        // Add LIMIT clause
        if ($args['limit'] > 0) {
            $sql .= " LIMIT " . intval($args['offset']) . ", " . intval($args['limit']);
        }

        $invoices = $wpdb->get_results($sql);

        // Decode JSON data for each invoice
        foreach ($invoices as $invoice) {
            $invoice->invoice_data = json_decode($invoice->invoice_data, true) ?: array();
        }

        return $invoices;
    }

    /**
     * Sanitize invoice data
     *
     * @param array $data Raw invoice data
     * @return array Sanitized invoice data
     * @since 1.0.0
     */
    private function si_sanitize_invoice_data($data) {
        return array(
            'invoice_number' => si_sanitize_text($data['invoice_number'] ?? ''),
            'client_id' => intval($data['client_id'] ?? 0),
            'template_id' => intval($data['template_id'] ?? 0),
            'invoice_data' => $data['invoice_data'] ?? array(),
            'status' => si_sanitize_text($data['status'] ?? 'draft')
        );
    }

    /**
     * Validate invoice data
     *
     * @param array $data Sanitized invoice data
     * @return bool True if valid, false otherwise
     * @since 1.0.0
     */
    private function si_validate_invoice_data($data) {
        // Check required fields
        if (empty($data['client_id']) || empty($data['template_id'])) {
            return false;
        }

        // Verify client exists
        $client_manager = new SI_Client();
        $client = $client_manager->si_get_client($data['client_id']);
        if (!$client) {
            return false;
        }

        // Verify template exists
        $template_manager = new SI_Template();
        $template = $template_manager->si_get_template($data['template_id']);
        if (!$template) {
            return false;
        }

        return true;
    }

    /**
     * Calculate total amount from invoice data
     *
     * @param array $invoice_data Invoice data
     * @return float Total amount
     * @since 1.0.0
     */
    private function si_calculate_total($invoice_data) {
        $subtotal = 0;

        // Calculate subtotal from items
        if (isset($invoice_data['items']) && is_array($invoice_data['items'])) {
            foreach ($invoice_data['items'] as $item) {
                $quantity = floatval($item['quantity'] ?? 0);
                $rate = floatval($item['rate'] ?? 0);
                $subtotal += $quantity * $rate;
            }
        }

        // Apply tax, discount, and shipping
        $tax_rate = floatval($invoice_data['tax_rate'] ?? 0) / 100;
        $tax_amount = $subtotal * $tax_rate;
        $discount = floatval($invoice_data['discount'] ?? 0);
        $shipping = floatval($invoice_data['shipping'] ?? 0);

        $total = $subtotal + $tax_amount - $discount + $shipping;

        return max(0, $total); // Ensure total is not negative
    }

    /**
     * Prepare data for template rendering
     *
     * @param object $invoice Invoice object
     * @param object $client Client object
     * @param object $template Template object
     * @param array $settings Plugin settings
     * @return array Template data
     * @since 1.0.0
     */
    private function si_prepare_template_data($invoice, $client, $template, $settings) {
        $invoice_data = $invoice->invoice_data;

        // Calculate due date
        $due_days = intval($settings['default_due_days'] ?? 7);
        $due_date = date('Y-m-d', strtotime($invoice->created_at . " +{$due_days} days"));

        // Generate UPI QR code if UPI ID is set
        $upi_qr_code = '';
        if (!empty($settings['upi_id'])) {
            $upi_string = si_generate_upi_string(
                $settings['upi_id'],
                $invoice->total_amount,
                'Invoice ' . $invoice->invoice_number
            );
            $upi_qr_code = $this->si_generate_qr_code($upi_string);
        }

        return array(
            'settings' => $settings,
            'client' => $client,
            'template' => $template,
            'invoice_number' => $invoice->invoice_number,
            'invoice_date' => date('Y-m-d', strtotime($invoice->created_at)),
            'due_date' => $due_date,
            'items' => $invoice_data['items'] ?? array(),
            'subtotal' => $this->si_calculate_subtotal($invoice_data),
            'tax_rate' => floatval($invoice_data['tax_rate'] ?? 0),
            'tax_amount' => $this->si_calculate_tax($invoice_data),
            'discount_amount' => floatval($invoice_data['discount'] ?? 0),
            'shipping_amount' => floatval($invoice_data['shipping'] ?? 0),
            'total_amount' => $invoice->total_amount,
            'upi_qr_code' => $upi_qr_code,
            'notes' => $invoice_data['notes'] ?? ''
        );
    }

    /**
     * Calculate subtotal from invoice data
     *
     * @param array $invoice_data Invoice data
     * @return float Subtotal
     * @since 1.0.0
     */
    private function si_calculate_subtotal($invoice_data) {
        $subtotal = 0;

        if (isset($invoice_data['items']) && is_array($invoice_data['items'])) {
            foreach ($invoice_data['items'] as $item) {
                $quantity = floatval($item['quantity'] ?? 0);
                $rate = floatval($item['rate'] ?? 0);
                $subtotal += $quantity * $rate;
            }
        }

        return $subtotal;
    }

    /**
     * Calculate tax amount from invoice data
     *
     * @param array $invoice_data Invoice data
     * @return float Tax amount
     * @since 1.0.0
     */
    private function si_calculate_tax($invoice_data) {
        $subtotal = $this->si_calculate_subtotal($invoice_data);
        $tax_rate = floatval($invoice_data['tax_rate'] ?? 0) / 100;
        
        return $subtotal * $tax_rate;
    }

    /**
     * Generate QR code for UPI payment
     *
     * @param string $data QR code data
     * @return string QR code image data or empty string
     * @since 1.0.0
     */
    private function si_generate_qr_code($data) {
        // Simple QR code generation using Google Charts API
        // In production, consider using a local QR code library
        $qr_url = 'https://chart.googleapis.com/chart?chs=200x200&cht=qr&chl=' . urlencode($data);
        
        return $qr_url;
    }

    /**
     * Create PDF from HTML content
     *
     * @param string $html_content HTML content
     * @param string $filename PDF filename
     * @param bool $download Whether to force download
     * @return string|false PDF content or false on failure
     * @since 1.0.0
     */
    private function si_create_pdf($html_content, $filename, $download = false) {
        // Try to load PDF libraries
        $this->si_load_pdf_libraries();

        // Check if TCPDF is available
        if (class_exists('TCPDF')) {
            return $this->si_create_pdf_with_tcpdf($html_content, $filename, $download);
        }

        // Check if mPDF is available (would need to be included separately)
        if (class_exists('Mpdf\Mpdf')) {
            return $this->si_create_pdf_with_mpdf($html_content, $filename, $download);
        }

        // Check if DomPDF is available
        if (class_exists('Dompdf\Dompdf')) {
            return $this->si_create_pdf_with_dompdf($html_content, $filename, $download);
        }

        // Use built-in PHP PDF generation as fallback
        if ($download) {
            return $this->si_create_simple_pdf($html_content, $filename);
        }

        return $html_content;
    }

    /**
     * Try to load available PDF libraries
     *
     * @since 1.0.0
     */
    private function si_load_pdf_libraries() {
        // Try to include TCPDF if available in WordPress plugins or themes
        $possible_tcpdf_paths = array(
            ABSPATH . 'wp-content/plugins/tcpdf/tcpdf.php',
            ABSPATH . 'wp-content/themes/' . get_template() . '/tcpdf/tcpdf.php',
            ABSPATH . 'wp-includes/tcpdf/tcpdf.php',
        );

        foreach ($possible_tcpdf_paths as $path) {
            if (file_exists($path) && !class_exists('TCPDF')) {
                require_once $path;
                break;
            }
        }

        // Try to include other PDF libraries if available
        $possible_mpdf_paths = array(
            ABSPATH . 'wp-content/plugins/mpdf/vendor/autoload.php',
            ABSPATH . 'wp-content/themes/' . get_template() . '/mpdf/vendor/autoload.php',
        );

        foreach ($possible_mpdf_paths as $path) {
            if (file_exists($path) && !class_exists('Mpdf\Mpdf')) {
                require_once $path;
                break;
            }
        }
    }

    /**
     * Output improved HTML content as downloadable PDF-like file
     *
     * @param string $html_content HTML content
     * @param string $filename Filename
     * @since 1.0.0
     */
    private function si_output_improved_pdf($html_content, $filename) {
        // Clean any previous output
        if (ob_get_level()) {
            ob_end_clean();
        }

        // Set headers for PDF-like download
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . sanitize_file_name($filename) . '.html"');
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

        // Enhanced print styles and auto-print functionality
        $enhanced_styles = '
        <style media="all">
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background: white;
                color: #333;
                line-height: 1.4;
            }

            @media print {
                @page {
                    margin: 1cm;
                    size: A4;
                }
                body {
                    font-size: 12pt;
                    background: white !important;
                    -webkit-print-color-adjust: exact;
                    print-color-adjust: exact;
                    margin: 0;
                    padding: 0;
                }
                .no-print {
                    display: none !important;
                }
                .invoice-container {
                    box-shadow: none !important;
                    border: none !important;
                    margin: 0 !important;
                    padding: 0 !important;
                }
            }

            .pdf-notice {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                margin-bottom: 20px;
                border-radius: 8px;
                text-align: center;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            }

            .pdf-notice h3 {
                margin: 0 0 15px 0;
                font-size: 18px;
                font-weight: bold;
            }

            .pdf-notice p {
                margin: 10px 0;
                font-size: 14px;
                opacity: 0.9;
            }

            .pdf-buttons {
                margin: 15px 0;
            }

            .pdf-button {
                background: rgba(255,255,255,0.2);
                color: white;
                border: 2px solid rgba(255,255,255,0.3);
                padding: 12px 24px;
                border-radius: 6px;
                cursor: pointer;
                margin: 0 8px;
                font-size: 14px;
                font-weight: bold;
                transition: all 0.3s ease;
                text-decoration: none;
                display: inline-block;
            }

            .pdf-button:hover {
                background: rgba(255,255,255,0.3);
                border-color: rgba(255,255,255,0.5);
                transform: translateY(-2px);
                color: white;
                text-decoration: none;
            }

            .pdf-button.primary {
                background: #4CAF50;
                border-color: #45a049;
            }

            .pdf-button.primary:hover {
                background: #45a049;
                border-color: #3d8b40;
            }

            .instructions {
                background: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                padding: 15px;
                margin-top: 15px;
                font-size: 13px;
                color: #6c757d;
            }

            .instructions strong {
                color: #495057;
            }
        </style>
        <script>
            function printInvoice() {
                window.print();
            }

            function downloadPDF() {
                if (window.confirm("This will open your browser\'s print dialog. Choose \'Save as PDF\' to download the invoice as a PDF file.")) {
                    window.print();
                }
            }

            // Auto-focus on print button for better UX
            window.onload = function() {
                document.getElementById("print-btn").focus();
            };
        </script>';

        // Enhanced print notice
        $print_notice = '
        <div class="pdf-notice no-print">
            <h3>📄 Invoice Ready for Download</h3>
            <p>Your invoice is ready! Use the buttons below to print or save as PDF:</p>
            <div class="pdf-buttons">
                <button id="print-btn" class="pdf-button primary" onclick="printInvoice()">🖨️ Print Invoice</button>
                <button class="pdf-button" onclick="downloadPDF()">💾 Save as PDF</button>
            </div>
            <div class="instructions">
                <strong>To save as PDF:</strong> Click "Save as PDF" button, then in the print dialog choose "Save as PDF" or "Microsoft Print to PDF" as your destination.
            </div>
        </div>';

        // Insert enhanced styles before closing head tag
        $html_content = str_replace('</head>', $enhanced_styles . '</head>', $html_content);

        // Insert print notice after opening body tag
        $html_content = str_replace('<body>', '<body>' . $print_notice, $html_content);

        echo $html_content;
        exit;
    }

    /**
     * Output HTML content as downloadable file
     *
     * @param string $html_content HTML content
     * @param string $filename Filename
     * @since 1.0.0
     */
    private function si_output_html_as_pdf($html_content, $filename) {
        // Clean any previous output
        if (ob_get_level()) {
            ob_end_clean();
        }

        // Set headers for download
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: inline; filename="' . sanitize_file_name($filename) . '.html"');
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

        // Add enhanced print styles and auto-print functionality
        $print_styles = '
        <style media="all">
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background: white;
            }
            @media print {
                @page {
                    margin: 1cm;
                    size: A4;
                }
                body {
                    font-size: 12pt;
                    background: white;
                    -webkit-print-color-adjust: exact;
                    print-color-adjust: exact;
                }
                .no-print {
                    display: none !important;
                }
            }
            .print-notice {
                background: #e3f2fd;
                border: 1px solid #2196f3;
                padding: 15px;
                margin-bottom: 20px;
                border-radius: 4px;
                text-align: center;
            }
            .print-button {
                background: #2196f3;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                cursor: pointer;
                margin: 0 5px;
            }
            .print-button:hover {
                background: #1976d2;
            }
        </style>
        <script>
            function printInvoice() {
                window.print();
            }
            function downloadPDF() {
                alert("To save as PDF: Use your browser\'s Print function and select \'Save as PDF\' as the destination.");
                window.print();
            }
        </script>';

        // Add print notice
        $print_notice = '
        <div class="print-notice no-print">
            <h3>Invoice Ready for Download</h3>
            <p>Use the buttons below to print or save this invoice as PDF:</p>
            <button class="print-button" onclick="printInvoice()">Print Invoice</button>
            <button class="print-button" onclick="downloadPDF()">Save as PDF</button>
            <p><small>To save as PDF: Click "Save as PDF" and choose "Save as PDF" in your browser\'s print dialog.</small></p>
        </div>';

        // Insert print styles before closing head tag
        $html_content = str_replace('</head>', $print_styles . '</head>', $html_content);

        // Insert print notice after opening body tag
        $html_content = str_replace('<body>', '<body>' . $print_notice, $html_content);

        echo $html_content;
        exit;
    }

    /**
     * Create PDF using TCPDF library
     *
     * @param string $html_content HTML content
     * @param string $filename PDF filename
     * @param bool $download Whether to force download
     * @return string|false PDF content or false on failure
     * @since 1.0.0
     */
    private function si_create_pdf_with_tcpdf($html_content, $filename, $download = false) {
        try {
            // Define TCPDF constants if not already defined
            if (!defined('PDF_PAGE_ORIENTATION')) define('PDF_PAGE_ORIENTATION', 'P');
            if (!defined('PDF_UNIT')) define('PDF_UNIT', 'mm');
            if (!defined('PDF_PAGE_FORMAT')) define('PDF_PAGE_FORMAT', 'A4');
            if (!defined('PDF_MARGIN_LEFT')) define('PDF_MARGIN_LEFT', 15);
            if (!defined('PDF_MARGIN_TOP')) define('PDF_MARGIN_TOP', 27);
            if (!defined('PDF_MARGIN_RIGHT')) define('PDF_MARGIN_RIGHT', 15);
            if (!defined('PDF_MARGIN_HEADER')) define('PDF_MARGIN_HEADER', 5);
            if (!defined('PDF_MARGIN_FOOTER')) define('PDF_MARGIN_FOOTER', 10);
            if (!defined('PDF_MARGIN_BOTTOM')) define('PDF_MARGIN_BOTTOM', 25);
            if (!defined('PDF_IMAGE_SCALE_RATIO')) define('PDF_IMAGE_SCALE_RATIO', 1.25);
            if (!defined('PDF_FONT_NAME_MAIN')) define('PDF_FONT_NAME_MAIN', 'helvetica');
            if (!defined('PDF_FONT_SIZE_MAIN')) define('PDF_FONT_SIZE_MAIN', 10);
            if (!defined('PDF_FONT_NAME_DATA')) define('PDF_FONT_NAME_DATA', 'helvetica');
            if (!defined('PDF_FONT_SIZE_DATA')) define('PDF_FONT_SIZE_DATA', 8);
            if (!defined('PDF_FONT_MONOSPACED')) define('PDF_FONT_MONOSPACED', 'courier');

            // Create new PDF document
            $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

            // Set document information
            $pdf->SetCreator('Simple Invoice Plugin');
            $pdf->SetAuthor(get_bloginfo('name'));
            $pdf->SetTitle('Invoice - ' . $filename);
            $pdf->SetSubject('Invoice');

            // Set default header data
            $pdf->SetHeaderData('', 0, 'Invoice', '');

            // Set header and footer fonts
            $pdf->setHeaderFont(Array(PDF_FONT_NAME_MAIN, '', PDF_FONT_SIZE_MAIN));
            $pdf->setFooterFont(Array(PDF_FONT_NAME_DATA, '', PDF_FONT_SIZE_DATA));

            // Set default monospaced font
            $pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);

            // Set margins
            $pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
            $pdf->SetHeaderMargin(PDF_MARGIN_HEADER);
            $pdf->SetFooterMargin(PDF_MARGIN_FOOTER);

            // Set auto page breaks
            $pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);

            // Set image scale factor
            $pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);

            // Add a page
            $pdf->AddPage();

            // Convert HTML to clean format for TCPDF
            $clean_html = $this->si_clean_html_for_tcpdf($html_content);

            // Write HTML content
            $pdf->writeHTML($clean_html, true, false, true, false, '');

            // Close and output PDF document
            if ($download) {
                // Clean any previous output
                if (ob_get_level()) {
                    ob_end_clean();
                }

                $pdf->Output(sanitize_file_name($filename) . '.pdf', 'D');
                exit;
            } else {
                return $pdf->Output('', 'S');
            }

        } catch (Exception $e) {
            error_log('TCPDF Error: ' . $e->getMessage());

            // Fallback to improved HTML output
            if ($download) {
                $this->si_output_improved_pdf($html_content, $filename);
                return true;
            }

            return false;
        }
    }

    /**
     * Clean HTML content for TCPDF compatibility
     *
     * @param string $html_content HTML content
     * @return string Cleaned HTML content
     * @since 1.0.0
     */
    private function si_clean_html_for_tcpdf($html_content) {
        // Remove DOCTYPE and html/head/body tags as TCPDF handles these
        $html_content = preg_replace('/<!DOCTYPE[^>]*>/i', '', $html_content);
        $html_content = preg_replace('/<html[^>]*>/i', '', $html_content);
        $html_content = preg_replace('/<\/html>/i', '', $html_content);
        $html_content = preg_replace('/<head[^>]*>.*?<\/head>/is', '', $html_content);
        $html_content = preg_replace('/<body[^>]*>/i', '', $html_content);
        $html_content = preg_replace('/<\/body>/i', '', $html_content);

        // Extract and apply CSS styles inline
        if (preg_match('/<style[^>]*>(.*?)<\/style>/is', $html_content, $matches)) {
            $css = $matches[1];
            $html_content = preg_replace('/<style[^>]*>.*?<\/style>/is', '', $html_content);

            // Apply basic CSS to HTML elements
            $html_content = $this->si_apply_css_to_html($html_content, $css);
        }

        // Remove script tags
        $html_content = preg_replace('/<script[^>]*>.*?<\/script>/is', '', $html_content);

        // Remove no-print elements
        $html_content = preg_replace('/<[^>]*class="[^"]*no-print[^"]*"[^>]*>.*?<\/[^>]*>/is', '', $html_content);

        return $html_content;
    }

    /**
     * Apply basic CSS styles to HTML elements
     *
     * @param string $html_content HTML content
     * @param string $css CSS content
     * @return string HTML with inline styles
     * @since 1.0.0
     */
    private function si_apply_css_to_html($html_content, $css) {
        // This is a simplified CSS to inline style converter
        // For production use, consider using a proper CSS parser

        // Apply basic styles
        $html_content = str_replace('<h1>', '<h1 >', $html_content);
        $html_content = str_replace('<h2>', '<h2 >', $html_content);
        $html_content = str_replace('<h3>', '<h3 >', $html_content);
        $html_content = str_replace('<table>', '<table >', $html_content);
        $html_content = str_replace('<th>', '<th >', $html_content);
        $html_content = str_replace('<td>', '<td >', $html_content);

        return $html_content;
    }

    /**
     * Create PDF using mPDF library
     *
     * @param string $html_content HTML content
     * @param string $filename PDF filename
     * @param bool $download Whether to force download
     * @return string|false PDF content or false on failure
     * @since 1.0.0
     */
    private function si_create_pdf_with_mpdf($html_content, $filename, $download = false) {
        try {
            $mpdf = new \Mpdf\Mpdf([
                'mode' => 'utf-8',
                'format' => 'A4',
                'margin_left' => 15,
                'margin_right' => 15,
                'margin_top' => 16,
                'margin_bottom' => 16,
                'margin_header' => 9,
                'margin_footer' => 9
            ]);

            $mpdf->SetTitle('Invoice - ' . $filename);
            $mpdf->SetAuthor(get_bloginfo('name'));
            $mpdf->WriteHTML($html_content);

            if ($download) {
                // Clean any previous output
                if (ob_get_level()) {
                    ob_end_clean();
                }

                $mpdf->Output(sanitize_file_name($filename) . '.pdf', 'D');
                exit;
            } else {
                return $mpdf->Output('', 'S');
            }

        } catch (Exception $e) {
            error_log('mPDF Error: ' . $e->getMessage());

            // Fallback to improved HTML output
            if ($download) {
                $this->si_output_improved_pdf($html_content, $filename);
                return true;
            }

            return false;
        }
    }

    /**
     * Create PDF using DomPDF library
     *
     * @param string $html_content HTML content
     * @param string $filename PDF filename
     * @param bool $download Whether to force download
     * @return string|false PDF content or false on failure
     * @since 1.0.0
     */
    private function si_create_pdf_with_dompdf($html_content, $filename, $download = false) {
        try {
            $dompdf = new \Dompdf\Dompdf();
            $dompdf->loadHtml($html_content);
            $dompdf->setPaper('A4', 'portrait');
            $dompdf->render();

            if ($download) {
                // Clean any previous output
                if (ob_get_level()) {
                    ob_end_clean();
                }

                $dompdf->stream(sanitize_file_name($filename) . '.pdf', array('Attachment' => true));
                exit;
            } else {
                return $dompdf->output();
            }

        } catch (Exception $e) {
            error_log('DomPDF Error: ' . $e->getMessage());

            // Fallback to improved HTML output
            if ($download) {
                $this->si_output_improved_pdf($html_content, $filename);
                return true;
            }

            return false;
        }
    }

    /**
     * Create simple PDF using custom PDF generator
     *
     * @param string $html_content HTML content
     * @param string $filename PDF filename
     * @return bool True on success
     * @since 1.0.0
     */
    private function si_create_simple_pdf($html_content, $filename) {
        // Include the simple PDF class
        require_once WIMP_PLUGIN_PATH . 'includes/class-simple-pdf.php';

        // Clean any previous output
        if (ob_get_level()) {
            ob_end_clean();
        }

        // Create PDF instance
        $pdf = new SI_Simple_PDF();

        // Parse HTML and extract invoice data
        $invoice_data = $this->si_parse_invoice_html($html_content);

        // Debug: Log parsed data if WP_DEBUG is enabled
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('SI PDF - Parsed invoice data: ' . print_r($invoice_data, true));
            error_log('SI PDF - HTML content length: ' . strlen($html_content));
        }

        // If no data was parsed, create a basic PDF with raw content
        if (empty($invoice_data['business_name']) && empty($invoice_data['invoice_number']) && empty($invoice_data['items'])) {
            $this->si_create_fallback_pdf($pdf, $html_content, $filename);
        } else {
            // Build PDF content from parsed data
            $this->si_build_pdf_content($pdf, $invoice_data);
        }

        // Output PDF
        $pdf->output(sanitize_file_name($filename) . '.pdf', 'D');

        return true;
    }

    /**
     * Create PDF directly from template data
     *
     * @param array $template_data Template data
     * @param string $filename PDF filename
     * @return bool True on success
     * @since 1.0.0
     */
    private function si_create_pdf_from_data($template_data, $filename) {
        // Clean any previous output
        if (ob_get_level()) {
            ob_end_clean();
        }

        // Create a simple text-based PDF using basic PDF structure
        $pdf_content = $this->si_create_simple_text_pdf($template_data, $filename);

        // Set headers for PDF download
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . sanitize_file_name($filename) . '.pdf"');
        header('Content-Length: ' . strlen($pdf_content));
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

        echo $pdf_content;
        exit;
    }

    /**
     * Create simple text-based PDF
     *
     * @param array $template_data Template data
     * @param string $filename Filename
     * @return string PDF content
     * @since 1.0.0
     */
    private function si_create_simple_text_pdf($template_data, $filename) {
        // Create a more professional PDF with better formatting
        return $this->si_generate_professional_pdf($template_data);
    }

    /**
     * Generate professional PDF with proper layout
     *
     * @param array $template_data Template data
     * @return string PDF content
     * @since 1.0.0
     */
    private function si_generate_professional_pdf($template_data) {
        // Build PDF content with multiple text objects for better positioning
        $pdf_objects = array();
        $content_stream = '';

        $settings = $template_data['settings'] ?? array();
        $client = $template_data['client'] ?? null;
        $items = $template_data['items'] ?? array();

        // Start content stream
        $content_stream .= "BT\n";

        // Business Header (Large, Bold-style)
        $business_name = $settings['business_name'] ?? get_bloginfo('name');
        if ($business_name) {
            $content_stream .= "/F1 18 Tf\n";
            $content_stream .= "1 0 0 1 50 750 Tm\n";
            $content_stream .= "(" . $this->si_escape_pdf_text(strtoupper($business_name)) . ") Tj\n";
        }

        // Business Details (Smaller font)
        $content_stream .= "/F1 10 Tf\n";
        $y_pos = 725;
        if (!empty($settings['business_address'])) {
            $content_stream .= "1 0 0 1 50 {$y_pos} Tm\n";
            $content_stream .= "(" . $this->si_escape_pdf_text($settings['business_address']) . ") Tj\n";
            $y_pos -= 12;
        }
        if (!empty($settings['business_email'])) {
            $content_stream .= "1 0 0 1 50 {$y_pos} Tm\n";
            $content_stream .= "(Email: " . $this->si_escape_pdf_text($settings['business_email']) . ") Tj\n";
            $y_pos -= 12;
        }
        if (!empty($settings['business_phone'])) {
            $content_stream .= "1 0 0 1 50 {$y_pos} Tm\n";
            $content_stream .= "(Phone: " . $this->si_escape_pdf_text($settings['business_phone']) . ") Tj\n";
            $y_pos -= 12;
        }
        if (!empty($settings['gstin'])) {
            $content_stream .= "1 0 0 1 50 {$y_pos} Tm\n";
            $content_stream .= "(GSTIN: " . $this->si_escape_pdf_text($settings['gstin']) . ") Tj\n";
            $y_pos -= 12;
        }

        // INVOICE Title (Right aligned, large)
        $content_stream .= "/F1 24 Tf\n";
        $content_stream .= "1 0 0 1 450 750 Tm\n";
        $content_stream .= "(INVOICE) Tj\n";

        // Invoice Details Box (Right side)
        $content_stream .= "/F1 10 Tf\n";
        $y_pos = 710;
        if (!empty($template_data['invoice_number'])) {
            $content_stream .= "1 0 0 1 450 {$y_pos} Tm\n";
            $content_stream .= "(Invoice #: " . $this->si_escape_pdf_text($template_data['invoice_number']) . ") Tj\n";
            $y_pos -= 12;
        }
        if (!empty($template_data['invoice_date'])) {
            $content_stream .= "1 0 0 1 450 {$y_pos} Tm\n";
            $content_stream .= "(Date: " . $this->si_escape_pdf_text($template_data['invoice_date']) . ") Tj\n";
            $y_pos -= 12;
        }
        if (!empty($template_data['due_date'])) {
            $content_stream .= "1 0 0 1 450 {$y_pos} Tm\n";
            $content_stream .= "(Due Date: " . $this->si_escape_pdf_text($template_data['due_date']) . ") Tj\n";
            $y_pos -= 12;
        }

        // Client Details (Left side, lower)
        $content_stream .= "/F1 12 Tf\n";
        $content_stream .= "1 0 0 1 50 600 Tm\n";
        $content_stream .= "(BILL TO:) Tj\n";

        $content_stream .= "/F1 10 Tf\n";
        $y_pos = 582;
        if ($client) {
            if (!empty($client->name)) {
                $content_stream .= "1 0 0 1 50 {$y_pos} Tm\n";
                $content_stream .= "(" . $this->si_escape_pdf_text($client->name) . ") Tj\n";
                $y_pos -= 12;
            }
            if (!empty($client->business_name)) {
                $content_stream .= "1 0 0 1 50 {$y_pos} Tm\n";
                $content_stream .= "(" . $this->si_escape_pdf_text($client->business_name) . ") Tj\n";
                $y_pos -= 12;
            }
            if (!empty($client->address)) {
                $content_stream .= "1 0 0 1 50 {$y_pos} Tm\n";
                $content_stream .= "(" . $this->si_escape_pdf_text($client->address) . ") Tj\n";
                $y_pos -= 12;
            }
            if (!empty($client->email)) {
                $content_stream .= "1 0 0 1 50 {$y_pos} Tm\n";
                $content_stream .= "(" . $this->si_escape_pdf_text($client->email) . ") Tj\n";
                $y_pos -= 12;
            }
            if (!empty($client->contact_number)) {
                $content_stream .= "1 0 0 1 50 {$y_pos} Tm\n";
                $content_stream .= "(" . $this->si_escape_pdf_text($client->contact_number) . ") Tj\n";
                $y_pos -= 12;
            }
        }

        // Items Table Header with separator line
        $content_stream .= "/F1 11 Tf\n";
        $content_stream .= "1 0 0 1 50 490 Tm\n";
        $content_stream .= "(ITEMS:) Tj\n";

        // Header separator line
        $content_stream .= "50 485 m 550 485 l S\n";

        $content_stream .= "/F1 10 Tf\n";
        $content_stream .= "1 0 0 1 50 470 Tm\n";
        $content_stream .= "(DESCRIPTION) Tj\n";

        $content_stream .= "1 0 0 1 300 470 Tm\n";
        $content_stream .= "(QTY) Tj\n";

        $content_stream .= "1 0 0 1 380 470 Tm\n";
        $content_stream .= "(RATE) Tj\n";

        $content_stream .= "1 0 0 1 480 470 Tm\n";
        $content_stream .= "(AMOUNT) Tj\n";

        // Column separator line
        $content_stream .= "50 465 m 550 465 l S\n";

        // Items Table Content
        $y_pos = 450;
        $content_stream .= "/F1 10 Tf\n";

        if (!empty($items)) {
            foreach ($items as $item) {
                $desc = $item['description'] ?? $item[1] ?? '';
                $qty = $item['quantity'] ?? $item[2] ?? '';
                $rate = $item['rate'] ?? $item[3] ?? '';
                $amount = ($qty && $rate) ? ($qty * $rate) : '';

                // Description
                $content_stream .= "1 0 0 1 50 {$y_pos} Tm\n";
                $content_stream .= "(" . $this->si_escape_pdf_text(substr($desc, 0, 35)) . ") Tj\n";

                // Quantity
                $content_stream .= "1 0 0 1 300 {$y_pos} Tm\n";
                $content_stream .= "(" . $this->si_escape_pdf_text($qty) . ") Tj\n";

                // Rate
                $content_stream .= "1 0 0 1 380 {$y_pos} Tm\n";
                $content_stream .= "(" . $this->si_escape_pdf_text($this->si_format_currency_safe($rate)) . ") Tj\n";

                // Amount
                $content_stream .= "1 0 0 1 480 {$y_pos} Tm\n";
                $content_stream .= "(" . $this->si_escape_pdf_text($this->si_format_currency_safe($amount)) . ") Tj\n";

                $y_pos -= 15;
            }
        }

        // Summary Section with separator
        $y_pos -= 10;
        $content_stream .= "400 {$y_pos} m 550 {$y_pos} l S\n";
        $y_pos -= 15;
        $content_stream .= "/F1 10 Tf\n";

        if (isset($template_data['subtotal'])) {
            $content_stream .= "1 0 0 1 400 {$y_pos} Tm\n";
            $content_stream .= "(Subtotal:) Tj\n";
            $content_stream .= "1 0 0 1 480 {$y_pos} Tm\n";
            $content_stream .= "(" . $this->si_escape_pdf_text($this->si_format_currency_safe($template_data['subtotal'])) . ") Tj\n";
            $y_pos -= 15;
        }

        if (isset($template_data['tax_amount']) && $template_data['tax_amount'] > 0) {
            $tax_label = 'Tax';
            if (isset($template_data['tax_rate']) && $template_data['tax_rate'] > 0) {
                $tax_label .= ' (' . $template_data['tax_rate'] . '%)';
            }
            $content_stream .= "1 0 0 1 400 {$y_pos} Tm\n";
            $content_stream .= "(" . $this->si_escape_pdf_text($tax_label . ':') . ") Tj\n";
            $content_stream .= "1 0 0 1 480 {$y_pos} Tm\n";
            $content_stream .= "(" . $this->si_escape_pdf_text($this->si_format_currency_safe($template_data['tax_amount'])) . ") Tj\n";
            $y_pos -= 15;
        }

        if (isset($template_data['total_amount'])) {
            $content_stream .= "/F1 12 Tf\n";
            $content_stream .= "1 0 0 1 400 {$y_pos} Tm\n";
            $content_stream .= "(TOTAL:) Tj\n";
            $content_stream .= "1 0 0 1 480 {$y_pos} Tm\n";
            $content_stream .= "(" . $this->si_escape_pdf_text($this->si_format_currency_safe($template_data['total_amount'])) . ") Tj\n";
            $y_pos -= 20;
        }

        // Notes Section
        if (!empty($template_data['notes'])) {
            $y_pos -= 10;
            $content_stream .= "/F1 11 Tf\n";
            $content_stream .= "1 0 0 1 50 {$y_pos} Tm\n";
            $content_stream .= "(NOTES:) Tj\n";
            $y_pos -= 15;

            $content_stream .= "/F1 10 Tf\n";
            $content_stream .= "1 0 0 1 50 {$y_pos} Tm\n";
            $content_stream .= "(" . $this->si_escape_pdf_text($template_data['notes']) . ") Tj\n";
        }

        $content_stream .= "ET\n";

        // Generate PDF with the content stream
        return $this->si_build_pdf_with_content($content_stream);
    }

    /**
     * Escape text for PDF content
     *
     * @param string $text Text to escape
     * @return string Escaped text
     * @since 1.0.0
     */
    private function si_escape_pdf_text($text) {
        $text = str_replace('\\', '\\\\', $text);
        $text = str_replace('(', '\\(', $text);
        $text = str_replace(')', '\\)', $text);
        $text = preg_replace('/[^\x20-\x7E]/', '', $text);
        return $text;
    }

    /**
     * Build PDF with content stream
     *
     * @param string $content_stream PDF content stream
     * @return string PDF content
     * @since 1.0.0
     */
    private function si_build_pdf_with_content($content_stream) {
        // Build complete PDF with proper offsets
        $pdf = "%PDF-1.4\n";
        $offsets = array();

        // Track current position
        $current_pos = strlen($pdf);

        // Object 1: Catalog
        $offsets[1] = $current_pos;
        $obj1 = "1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n";
        $pdf .= $obj1;
        $current_pos += strlen($obj1);

        // Object 2: Pages
        $offsets[2] = $current_pos;
        $obj2 = "2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n";
        $pdf .= $obj2;
        $current_pos += strlen($obj2);

        // Object 3: Page
        $offsets[3] = $current_pos;
        $obj3 = "3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n/Contents 4 0 R\n/Resources <<\n/Font <<\n/F1 5 0 R\n>>\n>>\n>>\nendobj\n";
        $pdf .= $obj3;
        $current_pos += strlen($obj3);

        // Object 4: Content stream
        $offsets[4] = $current_pos;
        $obj4 = "4 0 obj\n<<\n/Length " . strlen($content_stream) . "\n>>\nstream\n" . $content_stream . "\nendstream\nendobj\n";
        $pdf .= $obj4;
        $current_pos += strlen($obj4);

        // Object 5: Font
        $offsets[5] = $current_pos;
        $obj5 = "5 0 obj\n<<\n/Type /Font\n/Subtype /Type1\n/BaseFont /Helvetica\n>>\nendobj\n";
        $pdf .= $obj5;
        $current_pos += strlen($obj5);

        // Cross-reference table
        $xref_offset = $current_pos;
        $pdf .= "xref\n0 6\n0000000000 65535 f \n";

        for ($i = 1; $i <= 5; $i++) {
            $pdf .= sprintf("%010d 00000 n \n", $offsets[$i]);
        }

        // Trailer
        $pdf .= "trailer\n<<\n/Size 6\n/Root 1 0 R\n>>\nstartxref\n{$xref_offset}\n%%EOF\n";

        return $pdf;
    }

    /**
     * Generate PDF from text content
     *
     * @param string $text_content Text content
     * @return string PDF content
     * @since 1.0.0
     */
    private function si_generate_text_pdf($text_content) {
        // Escape text for PDF
        $text_content = str_replace('\\', '\\\\', $text_content);
        $text_content = str_replace('(', '\\(', $text_content);
        $text_content = str_replace(')', '\\)', $text_content);

        // Split into lines
        $lines = explode("\n", $text_content);

        // Build PDF content stream
        $stream_content = "BT\n/F1 10 Tf\n50 750 Td\n";

        foreach ($lines as $line) {
            $line = trim($line);
            $stream_content .= "(" . $line . ") Tj\n";
            $stream_content .= "0 -12 Td\n";
        }

        $stream_content .= "ET\n";

        // Build complete PDF with proper offsets
        $pdf = "%PDF-1.4\n";
        $objects = array();
        $offsets = array();

        // Track current position
        $current_pos = strlen($pdf);

        // Object 1: Catalog
        $offsets[1] = $current_pos;
        $obj1 = "1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n";
        $pdf .= $obj1;
        $current_pos += strlen($obj1);

        // Object 2: Pages
        $offsets[2] = $current_pos;
        $obj2 = "2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n";
        $pdf .= $obj2;
        $current_pos += strlen($obj2);

        // Object 3: Page
        $offsets[3] = $current_pos;
        $obj3 = "3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n/Contents 4 0 R\n/Resources <<\n/Font <<\n/F1 5 0 R\n>>\n>>\n>>\nendobj\n";
        $pdf .= $obj3;
        $current_pos += strlen($obj3);

        // Object 4: Content stream
        $offsets[4] = $current_pos;
        $obj4 = "4 0 obj\n<<\n/Length " . strlen($stream_content) . "\n>>\nstream\n" . $stream_content . "\nendstream\nendobj\n";
        $pdf .= $obj4;
        $current_pos += strlen($obj4);

        // Object 5: Font
        $offsets[5] = $current_pos;
        $obj5 = "5 0 obj\n<<\n/Type /Font\n/Subtype /Type1\n/BaseFont /Helvetica\n>>\nendobj\n";
        $pdf .= $obj5;
        $current_pos += strlen($obj5);

        // Cross-reference table
        $xref_offset = $current_pos;
        $pdf .= "xref\n0 6\n0000000000 65535 f \n";

        for ($i = 1; $i <= 5; $i++) {
            $pdf .= sprintf("%010d 00000 n \n", $offsets[$i]);
        }

        // Trailer
        $pdf .= "trailer\n<<\n/Size 6\n/Root 1 0 R\n>>\nstartxref\n{$xref_offset}\n%%EOF\n";

        return $pdf;
    }

    /**
     * Build PDF content from template data
     *
     * @param SI_Simple_PDF $pdf PDF instance
     * @param array $template_data Template data
     * @since 1.0.0
     */
    private function si_build_pdf_from_template_data($pdf, $template_data) {
        $settings = $template_data['settings'] ?? array();
        $client = $template_data['client'] ?? null;

        // Business Header
        $business_name = $settings['business_name'] ?? get_bloginfo('name');
        if (!empty($business_name)) {
            $pdf->add_title($business_name, 20);
            $pdf->add_line_break(5);
        }

        // Business details
        if (!empty($settings['business_address'])) {
            $pdf->add_text($settings['business_address'], 50, null, 10);
            $pdf->add_line_break();
        }
        if (!empty($settings['business_email'])) {
            $pdf->add_text('Email: ' . $settings['business_email'], 50, null, 10);
            $pdf->add_line_break();
        }
        if (!empty($settings['business_phone'])) {
            $pdf->add_text('Phone: ' . $settings['business_phone'], 50, null, 10);
            $pdf->add_line_break();
        }
        if (!empty($settings['gstin'])) {
            $pdf->add_text('GSTIN: ' . $settings['gstin'], 50, null, 10);
            $pdf->add_line_break();
        }

        $pdf->add_line_break(10);
        $pdf->add_title('INVOICE', 24);
        $pdf->add_line_break(15);

        // Invoice details section
        $pdf->add_heading('Invoice Details', 14);
        if (!empty($template_data['invoice_number'])) {
            $pdf->add_text('Invoice #: ' . $template_data['invoice_number'], 50, null, 12);
            $pdf->add_line_break();
        }
        if (!empty($template_data['invoice_date'])) {
            $pdf->add_text('Date: ' . $template_data['invoice_date'], 50, null, 12);
            $pdf->add_line_break();
        }
        if (!empty($template_data['due_date'])) {
            $pdf->add_text('Due Date: ' . $template_data['due_date'], 50, null, 12);
            $pdf->add_line_break(20);
        }

        // Client details section
        if ($client) {
            $pdf->add_heading('Bill To', 14);
            if (!empty($client->name)) {
                $pdf->add_text($client->name, 50, null, 12);
                $pdf->add_line_break();
            }
            if (!empty($client->business_name)) {
                $pdf->add_text($client->business_name, 50, null, 10);
                $pdf->add_line_break();
            }
            if (!empty($client->address)) {
                $pdf->add_text($client->address, 50, null, 10);
                $pdf->add_line_break();
            }
            if (!empty($client->email)) {
                $pdf->add_text($client->email, 50, null, 10);
                $pdf->add_line_break();
            }
            if (!empty($client->contact_number)) {
                $pdf->add_text($client->contact_number, 50, null, 10);
                $pdf->add_line_break();
            }
            if (!empty($client->gstin)) {
                $pdf->add_text('GSTIN: ' . $client->gstin, 50, null, 10);
                $pdf->add_line_break();
            }

            $pdf->add_line_break(15);
        }

        // Items table
        $items = $template_data['items'] ?? array();
        if (!empty($items)) {
            $pdf->add_heading('Items', 14);

            // Table header
            $pdf->add_table_row(array('Description', 'Qty', 'Rate', 'Amount'), 11);
            $pdf->add_line_break(5);

            // Table rows
            foreach ($items as $item) {
                $description = $item['description'] ?? $item[1] ?? '';
                $quantity = $item['quantity'] ?? $item[2] ?? '';
                $rate = $item['rate'] ?? $item[3] ?? '';
                $amount = ($quantity && $rate) ? $this->si_format_currency_safe($quantity * $rate) : '';

                $pdf->add_table_row(array(
                    $description,
                    $quantity,
                    $this->si_format_currency_safe($rate),
                    $amount
                ), 10);
            }

            $pdf->add_line_break(20);
        }

        // Summary section
        $pdf->add_heading('Summary', 14);
        if (isset($template_data['subtotal'])) {
            $pdf->add_text('Subtotal: ' . $this->si_format_currency_safe($template_data['subtotal']), 350, null, 12);
            $pdf->add_line_break();
        }
        if (isset($template_data['tax_amount']) && $template_data['tax_amount'] > 0) {
            $tax_label = 'Tax';
            if (isset($template_data['tax_rate']) && $template_data['tax_rate'] > 0) {
                $tax_label .= ' (' . $template_data['tax_rate'] . '%)';
            }
            $pdf->add_text($tax_label . ': ' . $this->si_format_currency_safe($template_data['tax_amount']), 350, null, 12);
            $pdf->add_line_break();
        }
        if (isset($template_data['discount_amount']) && $template_data['discount_amount'] > 0) {
            $pdf->add_text('Discount: -' . $this->si_format_currency_safe($template_data['discount_amount']), 350, null, 12);
            $pdf->add_line_break();
        }
        if (isset($template_data['shipping_amount']) && $template_data['shipping_amount'] > 0) {
            $pdf->add_text('Shipping: ' . $this->si_format_currency_safe($template_data['shipping_amount']), 350, null, 12);
            $pdf->add_line_break();
        }
        if (isset($template_data['total_amount'])) {
            $pdf->add_text('TOTAL: ' . $this->si_format_currency_safe($template_data['total_amount']), 350, null, 16);
            $pdf->add_line_break();
        }

        // Notes section
        if (!empty($template_data['notes'])) {
            $pdf->add_line_break(15);
            $pdf->add_heading('Notes', 14);
            $pdf->add_paragraph($template_data['notes'], 10);
        }
    }

    /**
     * Safe currency formatting with fallback
     *
     * @param float $amount Amount to format
     * @return string Formatted amount
     * @since 1.0.0
     */
    private function si_format_currency_safe($amount) {
        if (function_exists('si_format_currency')) {
            return si_format_currency($amount);
        }

        // Fallback formatting
        return '₹' . number_format((float)$amount, 2);
    }

    /**
     * Create fallback PDF when parsing fails
     *
     * @param SI_Simple_PDF $pdf PDF instance
     * @param string $html_content HTML content
     * @param string $filename Filename
     * @since 1.0.0
     */
    private function si_create_fallback_pdf($pdf, $html_content, $filename) {
        // Extract basic text content from HTML
        $text_content = strip_tags($html_content);
        $text_content = preg_replace('/\s+/', ' ', $text_content);
        $text_content = trim($text_content);

        // Add title
        $pdf->add_title('INVOICE', 24);
        $pdf->add_line_break(20);

        // Add content as paragraphs
        $lines = explode("\n", wordwrap($text_content, 80, "\n", true));
        foreach ($lines as $line) {
            $line = trim($line);
            if (!empty($line)) {
                $pdf->add_text($line, 50, null, 10);
                $pdf->add_line_break(12);
            }
        }
    }

    /**
     * Parse invoice HTML to extract structured data
     *
     * @param string $html_content HTML content
     * @return array Parsed invoice data
     * @since 1.0.0
     */
    private function si_parse_invoice_html($html_content) {
        $data = array(
            'business_name' => '',
            'business_address' => '',
            'business_email' => '',
            'business_phone' => '',
            'gstin' => '',
            'invoice_title' => 'INVOICE',
            'invoice_number' => '',
            'invoice_date' => '',
            'due_date' => '',
            'client_name' => '',
            'client_business' => '',
            'client_address' => '',
            'client_email' => '',
            'client_phone' => '',
            'client_gstin' => '',
            'items' => array(),
            'subtotal' => '',
            'tax_rate' => '',
            'tax_amount' => '',
            'discount' => '',
            'shipping' => '',
            'total' => '',
            'notes' => ''
        );

        // Extract business name
        if (preg_match('/<h2[^>]*class="business-name"[^>]*>(.*?)<\/h2>/is', $html_content, $matches)) {
            $data['business_name'] = trim(strip_tags($matches[1]));
        }

        // Extract business details from business-details div
        if (preg_match('/<div[^>]*class="business-details"[^>]*>(.*?)<\/div>/is', $html_content, $matches)) {
            $business_details = $matches[1];

            // Extract email
            if (preg_match('/Email:\s*([^<\n]+)/i', $business_details, $email_matches)) {
                $data['business_email'] = trim(strip_tags($email_matches[1]));
            }

            // Extract phone
            if (preg_match('/Phone:\s*([^<\n]+)/i', $business_details, $phone_matches)) {
                $data['business_phone'] = trim(strip_tags($phone_matches[1]));
            }

            // Extract GSTIN
            if (preg_match('/GSTIN:\s*([^<\n]+)/i', $business_details, $gstin_matches)) {
                $data['gstin'] = trim(strip_tags($gstin_matches[1]));
            }
        }

        // Extract invoice details
        if (preg_match('/<span[^>]*class="detail-label"[^>]*>Invoice #:<\/span>\s*<span[^>]*class="detail-value"[^>]*>(.*?)<\/span>/is', $html_content, $matches)) {
            $data['invoice_number'] = trim(strip_tags($matches[1]));
        }

        if (preg_match('/<span[^>]*class="detail-label"[^>]*>Date:<\/span>\s*<span[^>]*class="detail-value"[^>]*>(.*?)<\/span>/is', $html_content, $matches)) {
            $data['invoice_date'] = trim(strip_tags($matches[1]));
        }

        if (preg_match('/<span[^>]*class="detail-label"[^>]*>Due Date:<\/span>\s*<span[^>]*class="detail-value"[^>]*>(.*?)<\/span>/is', $html_content, $matches)) {
            $data['due_date'] = trim(strip_tags($matches[1]));
        }

        // Extract client details from client-details div
        if (preg_match('/<div[^>]*class="client-details"[^>]*>(.*?)<\/div>\s*<\/div>/is', $html_content, $matches)) {
            $client_section = $matches[1];

            // Extract client name (first strong tag)
            if (preg_match('/<strong>(.*?)<\/strong>/is', $client_section, $name_matches)) {
                $data['client_name'] = trim(strip_tags($name_matches[1]));
            }

            // Extract all detail items
            preg_match_all('/<div[^>]*class="detail-item"[^>]*>(.*?)<\/div>/is', $client_section, $detail_matches);
            foreach ($detail_matches[1] as $detail) {
                $detail_text = trim(strip_tags($detail));
                if (strpos($detail_text, '@') !== false && empty($data['client_email'])) {
                    $data['client_email'] = $detail_text;
                } elseif (preg_match('/^\+?[\d\s\-\(\)]+$/', $detail_text) && empty($data['client_phone'])) {
                    $data['client_phone'] = $detail_text;
                } elseif (strpos($detail_text, 'GSTIN:') !== false) {
                    $data['client_gstin'] = trim(str_replace('GSTIN:', '', $detail_text));
                } elseif (!empty($detail_text) && $detail_text !== $data['client_name'] && empty($data['client_address'])) {
                    $data['client_address'] = $detail_text;
                }
            }
        }

        // Extract table items
        if (preg_match('/<tbody>(.*?)<\/tbody>/is', $html_content, $matches)) {
            $tbody = $matches[1];
            preg_match_all('/<tr[^>]*>(.*?)<\/tr>/is', $tbody, $rows);

            foreach ($rows[1] as $row) {
                // Skip empty rows or "No items found" rows
                if (strpos($row, 'No items found') !== false) {
                    continue;
                }

                preg_match_all('/<td[^>]*>(.*?)<\/td>/is', $row, $cells);
                if (count($cells[1]) >= 4) {
                    $description = trim(strip_tags($cells[1][0]));
                    $quantity = trim(strip_tags($cells[1][1]));
                    $rate = trim(strip_tags($cells[1][2]));
                    $amount = trim(strip_tags($cells[1][3]));

                    if (!empty($description)) {
                        $data['items'][] = array(
                            'description' => $description,
                            'quantity' => $quantity,
                            'rate' => $rate,
                            'amount' => $amount
                        );
                    }
                }
            }
        }

        // Extract summary totals
        if (preg_match('/<td[^>]*class="summary-label"[^>]*>Subtotal:<\/td>\s*<td[^>]*class="summary-value"[^>]*>(.*?)<\/td>/is', $html_content, $matches)) {
            $data['subtotal'] = trim(strip_tags($matches[1]));
        }

        if (preg_match('/<td[^>]*class="summary-label"[^>]*>Tax[^<]*<\/td>\s*<td[^>]*class="summary-value"[^>]*>(.*?)<\/td>/is', $html_content, $matches)) {
            $data['tax_amount'] = trim(strip_tags($matches[1]));
        }

        if (preg_match('/<td[^>]*class="summary-label"[^>]*>Total:<\/td>\s*<td[^>]*class="summary-value"[^>]*>(.*?)<\/td>/is', $html_content, $matches)) {
            $data['total'] = trim(strip_tags($matches[1]));
        }

        return $data;
    }

    /**
     * Build PDF content from parsed data
     *
     * @param SI_Simple_PDF $pdf PDF instance
     * @param array $data Invoice data
     * @since 1.0.0
     */
    private function si_build_pdf_content($pdf, $data) {
        // Business Header
        if (!empty($data['business_name'])) {
            $pdf->add_title($data['business_name'], 20);
            $pdf->add_line_break(5);
        }

        // Business details
        if (!empty($data['business_address'])) {
            $pdf->add_text($data['business_address'], 50, null, 10);
            $pdf->add_line_break();
        }
        if (!empty($data['business_email'])) {
            $pdf->add_text('Email: ' . $data['business_email'], 50, null, 10);
            $pdf->add_line_break();
        }
        if (!empty($data['business_phone'])) {
            $pdf->add_text('Phone: ' . $data['business_phone'], 50, null, 10);
            $pdf->add_line_break();
        }
        if (!empty($data['gstin'])) {
            $pdf->add_text('GSTIN: ' . $data['gstin'], 50, null, 10);
            $pdf->add_line_break();
        }

        $pdf->add_line_break(10);
        $pdf->add_title($data['invoice_title'], 24);
        $pdf->add_line_break(15);

        // Invoice details section
        $pdf->add_heading('Invoice Details', 14);
        if (!empty($data['invoice_number'])) {
            $pdf->add_text('Invoice #: ' . $data['invoice_number'], 50, null, 12);
            $pdf->add_line_break();
        }
        if (!empty($data['invoice_date'])) {
            $pdf->add_text('Date: ' . $data['invoice_date'], 50, null, 12);
            $pdf->add_line_break();
        }
        if (!empty($data['due_date'])) {
            $pdf->add_text('Due Date: ' . $data['due_date'], 50, null, 12);
            $pdf->add_line_break(20);
        }

        // Client details section
        if (!empty($data['client_name'])) {
            $pdf->add_heading('Bill To', 14);
            $pdf->add_text($data['client_name'], 50, null, 12);
            $pdf->add_line_break();

            if (!empty($data['client_business'])) {
                $pdf->add_text($data['client_business'], 50, null, 10);
                $pdf->add_line_break();
            }
            if (!empty($data['client_address'])) {
                $pdf->add_text($data['client_address'], 50, null, 10);
                $pdf->add_line_break();
            }
            if (!empty($data['client_email'])) {
                $pdf->add_text($data['client_email'], 50, null, 10);
                $pdf->add_line_break();
            }
            if (!empty($data['client_phone'])) {
                $pdf->add_text($data['client_phone'], 50, null, 10);
                $pdf->add_line_break();
            }
            if (!empty($data['client_gstin'])) {
                $pdf->add_text('GSTIN: ' . $data['client_gstin'], 50, null, 10);
                $pdf->add_line_break();
            }

            $pdf->add_line_break(15);
        }

        // Items table
        if (!empty($data['items'])) {
            $pdf->add_heading('Items', 14);

            // Table header
            $pdf->add_table_row(array('Description', 'Qty', 'Rate', 'Amount'), 11);
            $pdf->add_line_break(5);

            // Table rows
            foreach ($data['items'] as $item) {
                $pdf->add_table_row(array(
                    $item['description'],
                    $item['quantity'],
                    $item['rate'],
                    $item['amount']
                ), 10);
            }

            $pdf->add_line_break(20);
        }

        // Summary section
        $pdf->add_heading('Summary', 14);
        if (!empty($data['subtotal'])) {
            $pdf->add_text('Subtotal: ' . $data['subtotal'], 350, null, 12);
            $pdf->add_line_break();
        }
        if (!empty($data['tax_amount'])) {
            $pdf->add_text('Tax: ' . $data['tax_amount'], 350, null, 12);
            $pdf->add_line_break();
        }
        if (!empty($data['discount'])) {
            $pdf->add_text('Discount: ' . $data['discount'], 350, null, 12);
            $pdf->add_line_break();
        }
        if (!empty($data['shipping'])) {
            $pdf->add_text('Shipping: ' . $data['shipping'], 350, null, 12);
            $pdf->add_line_break();
        }
        if (!empty($data['total'])) {
            $pdf->add_text('TOTAL: ' . $data['total'], 350, null, 16);
            $pdf->add_line_break();
        }

        // Notes section
        if (!empty($data['notes'])) {
            $pdf->add_line_break(15);
            $pdf->add_heading('Notes', 14);
            $pdf->add_paragraph($data['notes'], 10);
        }
    }





    /**
     * Handle PDF download requests
     *
     * @since 1.0.0
     */
    public function si_handle_pdf_download() {
        if (!isset($_GET['si_download_pdf']) || !isset($_GET['invoice_id'])) {
            return;
        }

        // Verify nonce
        if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'si_download_pdf')) {
            wp_die(__('Security check failed.', 'simple-invoice'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions.', 'simple-invoice'));
        }

        $invoice_id = intval($_GET['invoice_id']);

        if ($invoice_id > 0) {
            $this->si_generate_pdf($invoice_id, true);
            exit; // Exit after generating PDF
        } else {
            wp_die(__('Invalid invoice ID.', 'simple-invoice'));
        }
    }

    /**
     * AJAX handler for creating invoice
     *
     * @since 1.0.0
     */
    public function si_ajax_create_invoice() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'si_create_invoice_nonce')) {
            wp_send_json_error(__('Security check failed.', 'simple-invoice'));
            return;
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'simple-invoice'));
            return;
        }

        // Debug: Log the received data
        error_log('SI Invoice Creation - POST data: ' . print_r($_POST, true));

        // Create invoice
        $invoice_id = $this->si_create_invoice($_POST);

        if ($invoice_id) {
            $download_url = wp_nonce_url(
                add_query_arg(array(
                    'si_download_pdf' => '1',
                    'invoice_id' => $invoice_id
                ), admin_url()),
                'si_download_pdf',
                'nonce'
            );

            error_log('SI Invoice Created - ID: ' . $invoice_id . ', Download URL: ' . $download_url);

            wp_send_json_success(array(
                'message' => __('Invoice created successfully.', 'simple-invoice'),
                'invoice_id' => $invoice_id,
                'download_url' => $download_url
            ));
        } else {
            error_log('SI Invoice Creation Failed');
            wp_send_json_error(__('Failed to create invoice. Please check if client and template exist.', 'simple-invoice'));
        }
    }

    /**
     * AJAX handler for generating PDF
     *
     * @since 1.0.0
     */
    public function si_ajax_generate_pdf() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'si_generate_pdf_nonce')) {
            wp_send_json_error(__('Security check failed.', 'simple-invoice'));
            return;
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'simple-invoice'));
            return;
        }

        $invoice_id = intval($_POST['invoice_id'] ?? 0);

        if (!$invoice_id) {
            wp_send_json_error(__('Invalid invoice ID.', 'simple-invoice'));
            return;
        }

        $pdf_content = $this->si_generate_pdf($invoice_id);

        if ($pdf_content) {
            wp_send_json_success(array(
                'message' => __('PDF generated successfully.', 'simple-invoice'),
                'pdf_content' => base64_encode($pdf_content)
            ));
        } else {
            wp_send_json_error(__('Failed to generate PDF.', 'simple-invoice'));
        }
    }

    /**
     * AJAX handler for invoice preview
     *
     * @since 1.0.0
     */
    public function si_ajax_preview_invoice() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'si_preview_invoice_nonce')) {
            wp_send_json_error(__('Security check failed.', 'simple-invoice'));
            return;
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'simple-invoice'));
            return;
        }

        // Get preview data from POST
        $client_id = intval($_POST['client_id'] ?? 0);
        $template_id = intval($_POST['template_id'] ?? 0);
        $invoice_data = $_POST['invoice_data'] ?? array();

        // Get related data
        $client_manager = new SI_Client();
        $template_manager = new SI_Template();

        $client = $client_manager->si_get_client($client_id);
        $template = $template_manager->si_get_template($template_id);
        $settings = si_get_settings();

        if (!$client || !$template) {
            wp_send_json_error(__('Invalid client or template.', 'simple-invoice'));
            return;
        }

        // Create temporary invoice object for preview
        $temp_invoice = (object) array(
            'invoice_number' => 'PREVIEW-' . date('Y-m-d-His'),
            'created_at' => current_time('mysql'),
            'total_amount' => $this->si_calculate_total($invoice_data),
            'invoice_data' => $invoice_data
        );

        // Prepare template data
        $template_data = $this->si_prepare_template_data($temp_invoice, $client, $template, $settings);

        // Load design template
        $html_content = $this->si_get_design_loader()->si_load_design_template(
            $template->design ?? 'classic',
            $template_data
        );

        wp_send_json_success(array(
            'message' => __('Preview generated successfully.', 'simple-invoice'),
            'html_content' => $html_content
        ));
    }

    /**
     * Get invoices with filters
     *
     * @param string $search Search term
     * @param string $status Status filter
     * @param string $date_from Date from filter
     * @param string $date_to Date to filter
     * @param int $limit Number of invoices to return
     * @param int $offset Offset for pagination
     * @return array
     * @since 1.0.0
     */
    public function si_get_invoices_with_filters($search = '', $status = '', $date_from = '', $date_to = '', $limit = 20, $offset = 0) {
        global $wpdb;

        $where_conditions = array('1=1');
        $where_values = array();

        // Search filter
        if (!empty($search)) {
            $where_conditions[] = "(invoice_number LIKE %s OR id IN (SELECT id FROM {$wpdb->prefix}si_invoices WHERE client_id IN (SELECT id FROM {$wpdb->prefix}si_clients WHERE name LIKE %s OR business_name LIKE %s)))";
            $search_term = '%' . $wpdb->esc_like($search) . '%';
            $where_values[] = $search_term;
            $where_values[] = $search_term;
            $where_values[] = $search_term;
        }

        // Status filter
        if (!empty($status)) {
            $where_conditions[] = "status = %s";
            $where_values[] = $status;
        }

        // Date from filter
        if (!empty($date_from)) {
            $where_conditions[] = "DATE(created_at) >= %s";
            $where_values[] = $date_from;
        }

        // Date to filter
        if (!empty($date_to)) {
            $where_conditions[] = "DATE(created_at) <= %s";
            $where_values[] = $date_to;
        }

        $where_clause = implode(' AND ', $where_conditions);

        $query = $wpdb->prepare(
            "SELECT * FROM {$this->table_name} WHERE {$where_clause} ORDER BY created_at DESC LIMIT %d OFFSET %d",
            array_merge($where_values, array($limit, $offset))
        );

        return $wpdb->get_results($query);
    }

    /**
     * Count invoices with filters
     *
     * @param string $search Search term
     * @param string $status Status filter
     * @param string $date_from Date from filter
     * @param string $date_to Date to filter
     * @return int
     * @since 1.0.0
     */
    public function si_count_invoices_with_filters($search = '', $status = '', $date_from = '', $date_to = '') {
        global $wpdb;

        $where_conditions = array('1=1');
        $where_values = array();

        // Search filter
        if (!empty($search)) {
            $where_conditions[] = "(invoice_number LIKE %s OR id IN (SELECT id FROM {$wpdb->prefix}si_invoices WHERE client_id IN (SELECT id FROM {$wpdb->prefix}si_clients WHERE name LIKE %s OR business_name LIKE %s)))";
            $search_term = '%' . $wpdb->esc_like($search) . '%';
            $where_values[] = $search_term;
            $where_values[] = $search_term;
            $where_values[] = $search_term;
        }

        // Status filter
        if (!empty($status)) {
            $where_conditions[] = "status = %s";
            $where_values[] = $status;
        }

        // Date from filter
        if (!empty($date_from)) {
            $where_conditions[] = "DATE(created_at) >= %s";
            $where_values[] = $date_from;
        }

        // Date to filter
        if (!empty($date_to)) {
            $where_conditions[] = "DATE(created_at) <= %s";
            $where_values[] = $date_to;
        }

        $where_clause = implode(' AND ', $where_conditions);

        if (empty($where_values)) {
            $query = "SELECT COUNT(*) FROM {$this->table_name} WHERE {$where_clause}";
            return $wpdb->get_var($query);
        } else {
            $query = $wpdb->prepare(
                "SELECT COUNT(*) FROM {$this->table_name} WHERE {$where_clause}",
                $where_values
            );
            return $wpdb->get_var($query);
        }
    }

    /**
     * AJAX handler for viewing invoice
     *
     * @since 1.0.0
     */
    public function si_ajax_view_invoice() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'si_view_invoice_nonce')) {
            wp_send_json_error(__('Security check failed.', 'simple-invoice'));
            return;
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'simple-invoice'));
            return;
        }

        $invoice_id = intval($_POST['invoice_id'] ?? 0);

        if (!$invoice_id) {
            wp_send_json_error(__('Invalid invoice ID.', 'simple-invoice'));
            return;
        }

        $invoice = $this->si_get_invoice($invoice_id);

        if (!$invoice) {
            wp_send_json_error(__('Invoice not found.', 'simple-invoice'));
            return;
        }

        // Get related data
        $client_manager = new SI_Client();
        $template_manager = new SI_Template();

        $client = $client_manager->si_get_client($invoice->client_id);
        $template = $template_manager->si_get_template($invoice->template_id);
        $settings = si_get_settings();

        // Prepare data for template
        $template_data = $this->si_prepare_template_data($invoice, $client, $template, $settings);

        // Load design template
        $html_content = $this->si_get_design_loader()->si_load_design_template(
            $template->design ?? 'classic',
            $template_data
        );

        wp_send_json_success(array(
            'html_content' => $html_content
        ));
    }

    /**
     * AJAX handler for updating invoice status
     *
     * @since 1.0.0
     */
    public function si_ajax_update_invoice_status() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'si_update_status_nonce')) {
            wp_send_json_error(__('Security check failed.', 'simple-invoice'));
            return;
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'simple-invoice'));
            return;
        }

        $invoice_id = intval($_POST['invoice_id'] ?? 0);
        $status = sanitize_text_field($_POST['status'] ?? '');

        if (!$invoice_id || !$status) {
            wp_send_json_error(__('Invalid invoice ID or status.', 'simple-invoice'));
            return;
        }

        // Validate status
        $valid_statuses = array('draft', 'sent', 'paid', 'overdue');
        if (!in_array($status, $valid_statuses)) {
            wp_send_json_error(__('Invalid status.', 'simple-invoice'));
            return;
        }

        global $wpdb;
        $result = $wpdb->update(
            $this->table_name,
            array('status' => $status),
            array('id' => $invoice_id),
            array('%s'),
            array('%d')
        );

        if ($result !== false) {
            wp_send_json_success(array(
                'message' => __('Status updated successfully.', 'simple-invoice')
            ));
        } else {
            wp_send_json_error(__('Failed to update status.', 'simple-invoice'));
        }
    }

    /**
     * AJAX handler for deleting invoice
     *
     * @since 1.0.0
     */
    public function si_ajax_delete_invoice() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'si_delete_invoice_nonce')) {
            wp_send_json_error(__('Security check failed.', 'simple-invoice'));
            return;
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'simple-invoice'));
            return;
        }

        $invoice_id = intval($_POST['invoice_id'] ?? 0);

        if (!$invoice_id) {
            wp_send_json_error(__('Invalid invoice ID.', 'simple-invoice'));
            return;
        }

        global $wpdb;
        $result = $wpdb->delete(
            $this->table_name,
            array('id' => $invoice_id),
            array('%d')
        );

        if ($result !== false) {
            wp_send_json_success(array(
                'message' => __('Invoice deleted successfully.', 'simple-invoice')
            ));
        } else {
            wp_send_json_error(__('Failed to delete invoice.', 'simple-invoice'));
        }
    }
}
