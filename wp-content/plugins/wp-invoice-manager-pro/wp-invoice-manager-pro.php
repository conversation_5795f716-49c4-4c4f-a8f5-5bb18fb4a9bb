<?php
/**
 * Plugin Name: WordPress Invoice Manager Pro
 * Plugin URI: https://rektech.uk
 * Description: Professional WordPress invoice management solution with advanced customization, multiple templates, client management, and comprehensive billing features for businesses.
 * Version: 1.0.0
 * Author: RekTech
 * Author URI: https://rektech.uk
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: wp-invoice-manager-pro
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 *
 * @package WPInvoiceManagerPro
 * <AUTHOR>
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('WIMP_PLUGIN_VERSION', '1.0.0');
define('WIMP_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WIMP_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('WIMP_PLUGIN_BASENAME', plugin_basename(__FILE__));
define('WIMP_PLUGIN_FILE', __FILE__);

/**
 * Main WordPress Invoice Manager Pro Plugin Class
 *
 * @since 1.0.0
 */
final class WP_Invoice_Manager_Pro {

    /**
     * Plugin instance
     *
     * @var WP_Invoice_Manager_Pro
     * @since 1.0.0
     */
    private static $instance = null;

    /**
     * Get plugin instance
     *
     * @return WP_Invoice_Manager_Pro
     * @since 1.0.0
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    private function __construct() {
        $this->wimp_init_hooks();
    }

    /**
     * Initialize hooks
     *
     * @since 1.0.0
     */
    private function wimp_init_hooks() {
        add_action('init', array($this, 'wimp_init'));
        add_action('plugins_loaded', array($this, 'wimp_load_textdomain'));

        // Activation and deactivation hooks
        register_activation_hook(__FILE__, array($this, 'wimp_activate'));
        register_deactivation_hook(__FILE__, array($this, 'wimp_deactivate'));
    }

    /**
     * Initialize plugin
     *
     * @since 1.0.0
     */
    public function wimp_init() {
        // Load required files
        $this->wimp_load_files();

        // Initialize admin if in admin area
        if (is_admin()) {
            $this->wimp_init_admin();
        }

        // Initialize frontend
        $this->wimp_init_frontend();
    }

    /**
     * Load plugin files
     *
     * @since 1.0.0
     */
    private function wimp_load_files() {
        // Core classes - load in correct order
        require_once WIMP_PLUGIN_PATH . 'includes/functions.php';
        require_once WIMP_PLUGIN_PATH . 'includes/class-design-loader.php';
        require_once WIMP_PLUGIN_PATH . 'includes/class-client.php';
        require_once WIMP_PLUGIN_PATH . 'includes/class-template.php';
        require_once WIMP_PLUGIN_PATH . 'includes/class-simple-pdf.php';
        require_once WIMP_PLUGIN_PATH . 'includes/class-invoice.php';

        // Admin classes
        if (is_admin()) {
            require_once WIMP_PLUGIN_PATH . 'admin/class-admin.php';
        }
    }

    /**
     * Initialize admin
     *
     * @since 1.0.0
     */
    private function wimp_init_admin() {
        new SI_Admin();
    }

    /**
     * Initialize frontend
     *
     * @since 1.0.0
     */
    private function wimp_init_frontend() {
        // Frontend initialization if needed
        add_action('wp_enqueue_scripts', array($this, 'wimp_enqueue_frontend_scripts'));

        // Initialize core classes
        $this->wimp_init_core_classes();
    }

    /**
     * Initialize core classes
     *
     * @since 1.0.0
     */
    private function wimp_init_core_classes() {
        // Initialize classes that need to be instantiated
        new SI_Client();
        new SI_Template();
        new SI_Invoice();
        new SI_Design_Loader();
    }

    /**
     * Enqueue frontend scripts and styles
     *
     * @since 1.0.0
     */
    public function wimp_enqueue_frontend_scripts() {

        wp_enqueue_script(
            'wimp-frontend-script',
            WIMP_PLUGIN_URL . 'assets/js/frontend.js',
            array('jquery'),
            WIMP_PLUGIN_VERSION,
            true
        );
    }

    /**
     * Load plugin textdomain
     *
     * @since 1.0.0
     */
    public function wimp_load_textdomain() {
        load_plugin_textdomain(
            'wp-invoice-manager-pro',
            false,
            dirname(WIMP_PLUGIN_BASENAME) . '/languages'
        );
    }

    /**
     * Plugin activation
     *
     * @since 1.0.0
     */
    public function wimp_activate() {
        // Create database tables
        $this->wimp_create_tables();

        // Set default options
        $this->wimp_set_default_options();

        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Plugin deactivation
     *
     * @since 1.0.0
     */
    public function wimp_deactivate() {
        // Clear plugin data if enabled in settings
        if (function_exists('si_clear_all_plugin_data')) {
            si_clear_all_plugin_data();
        }

        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Create database tables
     *
     * @since 1.0.0
     */
    private function wimp_create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Clients table
        $clients_table = $wpdb->prefix . 'wimp_clients';
        $clients_sql = "CREATE TABLE $clients_table (
            id int(11) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            business_name varchar(255) DEFAULT '',
            address text DEFAULT '',
            contact_number varchar(50) DEFAULT '',
            email varchar(100) DEFAULT '',
            gstin varchar(50) DEFAULT '',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";

        // Templates table
        $templates_table = $wpdb->prefix . 'wimp_templates';
        $templates_sql = "CREATE TABLE $templates_table (
            id int(11) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            design varchar(100) DEFAULT 'classic',
            header_fields text DEFAULT '',
            body_fields text DEFAULT '',
            summary_fields text DEFAULT '',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY name (name)
        ) $charset_collate;";

        // Invoices table
        $invoices_table = $wpdb->prefix . 'wimp_invoices';
        $invoices_sql = "CREATE TABLE $invoices_table (
            id int(11) NOT NULL AUTO_INCREMENT,
            invoice_number varchar(100) NOT NULL,
            client_id int(11) NOT NULL,
            template_id int(11) NOT NULL,
            invoice_data longtext DEFAULT '',
            total_amount decimal(10,2) DEFAULT 0.00,
            status varchar(50) DEFAULT 'draft',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY invoice_number (invoice_number),
            KEY client_id (client_id),
            KEY template_id (template_id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($clients_sql);
        dbDelta($templates_sql);
        dbDelta($invoices_sql);
    }

    /**
     * Set default options
     *
     * @since 1.0.0
     */
    private function wimp_set_default_options() {
        $default_settings = array(
            'business_name' => get_bloginfo('name'),
            'business_address' => '',
            'business_email' => get_option('admin_email'),
            'business_phone' => '',
            'business_logo' => '',
            'gstin' => '',
            'default_due_days' => 7,
            'payment_methods' => array('bank', 'upi'),
            'bank_details' => '',
            'paypal_email' => '',
            'upi_id' => '',
            'footer_notes' => 'Thank you for your business!',
            'terms_text' => 'Payment is due within the specified due date.',
            'clear_data_on_deactivation' => 0
        );

        add_option('wimp_settings', $default_settings);
    }


}

/**
 * Initialize the plugin
 *
 * @since 1.0.0
 */
function wimp_init_plugin() {
    return WP_Invoice_Manager_Pro::get_instance();
}

// Initialize the plugin
wimp_init_plugin();
